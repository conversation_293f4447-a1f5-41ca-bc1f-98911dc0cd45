@if (loadingService.isLoading) {
<app-loading></app-loading>
}

<div class="vehicle-model-section">
  <div class="vehicle-model-section--box">
    <div class="vehicle-model-section__header">
      <h2 class="title-content-custom mb-0">
        {{ "vehicleModel.vehicleModel" | translate }}
      </h2>
      @if (!(screenSizeService.isMobile() | async)) {
      <div class="vehicle-model-section__button-group">
        <button
          *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_CREATE])"
          type="button"
          class="btn btn-secondary vehicle-model-section__button"
          (click)="onAddNewVehicleModel()"
        >
          {{ "vehicleModel.addNewBtn" | translate }}
        </button>
        <button
          *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_IMPORT])"
          type="button"
          class="btn btn-primary vehicle-model-section__button"
          (click)="importVehicleModel()"
        >
          {{ "vehicleModel.importBtn" | translate }}
        </button>
      </div>
      }
    </div>
    @if (!(screenSizeService.isMobile() | async)) {
    <form
      [formGroup]="vehicleModelFormSearch"
      (ngSubmit)="onSearch()"
      class="vehicle-model-section__form"
    >
      <div class="vehicle-model-section__form-group">
        <app-form-group
          [label]="'vehicleModel.modelSalesCode' | translate"
          [control]="modelSalesCodeControlSearch"
          [placeholder]="
            'vehicleModel.placeHolderModelSalesCodeSearch' | translate
          "
          controlId="modelSalesCode"
          class="vehicle-model-section__input"
        ></app-form-group>

        <app-form-group
          [label]="'vehicleModel.modelName' | translate"
          [control]="modelNameControlSearch"
          [placeholder]="'vehicleModel.placeHolderModelNameSearch' | translate"
          controlId="modelName"
          class="vehicle-model-section__input"
        ></app-form-group>

        <button
          type="submit"
          class="btn btn--primary vehicle-model-section__button"
        >
          {{ "common.search" | translate }}
        </button>
      </div>
    </form>
    }
    <app-ag-grid-custom
      class="custom-grid"
      [isPaging]="true"
      [pagingInfo]="pagingInfo"
      [rowData]="rowData"
      [colDefs]="colDefs"
      [isShowActionExport]="false"
      [defaultColDef]="defaultColDef"
      [templates]="customTemplates"
      (changeItemPerPage)="onResultsPerPageChange($event)"
      (onPageChange)="onPageChange($event)"
    ></app-ag-grid-custom>
    <ng-template #actionTemplate let-value="value">
      <app-action-cell-renderer
        [rowIndex]="value.rowIndex"
        (edit)="onEdit(value.rowIndex)"
        (remove)="onRemove(value.rowIndex)"
      ></app-action-cell-renderer>
    </ng-template>
  </div>
  <div 
    *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_IMPORT])" 
    class="import-result-summary">
    <app-import-result-summary
      [importDate]="importInfo?.date"
      [resultMessage]="importInfo?.resultMessage"
      [fileName]="
        importInfo?.failedRecords?.realFileName ||
        importInfo?.importFile?.realFileName
      "
      [downloadUrl]="
        importInfo?.failedRecords?.downloadUrl ||
        importInfo?.importFile?.downloadUrl
      "
      (downloadFile)="downloadImportResult()"
      [isLoading]="isLoadingLastImport"
    ></app-import-result-summary>
  </div>
</div>
