<div class="section-snap-shot-record">
  <div class="section-header">
    <mat-icon svgIcon="ic-snap-shot-record" class="medium-icon section-header--icon"></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.snapShotRecord.title" | translate }}</span>
    </div>
  </div>
  <div class="section-snap-shot-record__body">
    <ng-container *ngIf="data && data.length; else noData">
      @for (item of data; track $index) {
      <div class="section-snap-shot-record__body--item">
        <div class="section-snap-shot-record__body--item__content">
          <span>{{item?.subjectSSCCategory}}</span>
          <span>{{item?.validFrom}}</span>
        </div>
      </div>
      }
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">{{ 'common.noService' | translate }}</div>
    </ng-template>
  </div>
</div>