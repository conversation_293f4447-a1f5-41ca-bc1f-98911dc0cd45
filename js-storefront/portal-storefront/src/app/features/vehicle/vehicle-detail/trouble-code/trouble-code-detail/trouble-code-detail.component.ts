import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { AgGridCustomComponent } from '../../../../../core/shared';
import { DEFAULT_COL_DEF } from '../../../../../core/constants';
import {
  LoadingService,
  NotificationService,
  TroubleCodeService,
} from '../../../../../core/services';
import { handleErrors } from '../../../../../core/helpers';
import { ActivatedRoute } from '@angular/router';
import { TroubleCodeDetailItem, TroubleCodeDetail } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-trouble-code-detail',
  standalone: true,
  imports: [CommonModule, TranslateModule, IconModule, AgGridCustomComponent],
  templateUrl: './trouble-code-detail.component.html',
  styleUrl: './trouble-code-detail.component.scss',
  providers: [NotificationService],
})
export class TroubleCodeDetailComponent implements OnInit {
  @Input() id: string;
  @Output() backToList = new EventEmitter();

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  activatedRoute = inject(ActivatedRoute);

  troubleCodeService = inject(TroubleCodeService);

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    wrapText: true,
    autoHeight: true,
    flex: 1,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };

  rowData: TroubleCodeDetailItem[];
  colDefs: any;
  vin: string;
  troubleCodeDetail: TroubleCodeDetail;

  troubleCode: string;

  ngOnInit(): void {
    this.vin = this.activatedRoute.snapshot.paramMap.get('id');
    this.troubleCode = this.activatedRoute.snapshot.queryParamMap.get('troubleCode');
    this.getTroubleCodeDetail();
  }

  transformData(data = []) {
    const allDidNames = Array.from(
      new Set(data?.flatMap((item) => item?.didItems?.map((did) => did.didName)))
    );

    const tableData = allDidNames?.map((didName) => {
      const row: any = { didName }; //get first col
      data?.forEach((item) => {
        const found = item?.didItems?.find((d) => d.didName === didName);
        row[item.ssrNo] = found ? found.value : '-';
      });
      return row;
    });

    return tableData;
  }

  generateColDefs(data = []) {
    // get column dynamic from didName
    const allSignalNames = Array.from(new Set(data?.map((item) => item.ssrNo)));

    return [
      {
        headerName: this.translateService.instant(
          'troubleCode.detail.signalName'
        ),
        headerValueGetter: () =>
          this.translateService.instant('troubleCode.detail.signalName'),
        field: 'didName',
        pinned: 'left',
        cellClass: 'header-col',
        headerClass: 'header-signal',
      },
      ...allSignalNames?.map((ssrNo) => ({
        headerName: ssrNo,
        field: ssrNo,
        cellClassRules: {
          'highlight-column': (params) => params.column.getColId() === '01',
        },
        headerClass: (params) =>
          params.column.getColId() === '01' ? 'header-special' : '',
      })),
    ];
  }

  getTroubleCodeDetail(): void {
    this.loadingService.showLoader();
    this.troubleCodeService.getTroubleCodeDetail(this.vin, this.id || this.troubleCode).subscribe(
      (response) => {
        this.loadingService.hideLoader();
        this.troubleCodeDetail = response;
        this.rowData = this.transformData(response?.ssr);
        this.colDefs = this.generateColDefs(response?.ssr);
      },
      (error) => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService);
      }
    );
  }
}
