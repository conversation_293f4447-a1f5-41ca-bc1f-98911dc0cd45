import { Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  DropdownFormGroupComponent,
  FormGroupComponent,
} from '../../../../../core/shared';
import { ActionModal, ResolutionEmergency } from '../../../../../core/enums';
import {
  LoadingService,
  NotificationService,
  VehicleService,
} from '../../../../../core/services';
import { Subscription } from 'rxjs';
import { OptionDropdown } from '../../../../../core/interfaces';
import { handleErrors } from '../../../../../core/helpers';

@Component({
  selector: 'app-modal-close',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
  ],
  providers: [VehicleService, NotificationService],
  templateUrl: './modal-close.component.html',
  styleUrls: ['./modal-close.component.scss'],
})
export class ModalCloseComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCloseComponent>);
  vehicleService = inject(VehicleService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  mainSubscription = new Subscription();
  resolution: OptionDropdown[] = [];
  resolutionSelected: string = '';
  resolutionEmergency = ResolutionEmergency;
  ngOnInit() {
    this.getResolution();
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  getResolution() {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService.getResolution().subscribe(
        (res: OptionDropdown[]) => {
          if (res && res.length) {
            this.resolution = res.filter((item) => item.code !== '');
            if (this.resolution && this.resolution.length > 0)
              this.data?.emergencyFormClose?.controls['resolution'].patchValue(
                this.resolution[0].code
              );
              this.resolutionSelected = this.resolution[0].code;
            this.loadingService.hideLoader();
          }
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onResolutionChange(e: any) {
    this.resolutionSelected = e?.value || undefined;
    if (this.resolutionSelected === ResolutionEmergency.other) {
      this.data?.emergencyFormClose?.controls['comment']?.addValidators(
        Validators.required
      );
    } else {
      this.data?.emergencyFormClose?.controls['comment']?.clearValidators();
    }
    this.data?.emergencyFormClose?.controls[
      'comment'
    ]?.updateValueAndValidity();
  }
  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    const params = {
      vin: this.data.vin,
      ticketId: this.data.ticketId,
      resolution: this.resolution.find(
        (item) => item.code === this.resolutionSelected
      ),
      comment: this.data?.emergencyFormClose?.get('comment')?.value || '',
    };
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService.closeTheEmergency(params).subscribe(
        (res) => {
          if (res?.result === 'SUCCESS') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'vehicle.emergency.successfullyClosed'
              )
            );
            this.dialogRef.close({ action: ActionModal.Submit });
          } else {
            this.notificationService.showError(res?.message);
          }
          this.loadingService.hideLoader();
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }
}
