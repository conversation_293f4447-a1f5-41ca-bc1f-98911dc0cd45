<div class="modal-close">
  <h2 class="title-dialog">{{ data?.title }}</h2>

  <div class="container-dialog">
    <div class="confirm-msg" [innerHTML]="data?.confirmMsg"></div>
    <form [formGroup]="data?.emergencyFormClose">
      <div class="add-vehicle-model-section-form-group">
        <app-dropdown-form-group
          [label]="'vehicle.emergency.modal.resolution' | translate"
          [control]="data?.resolution"
          [options]="resolution"
          (changeOption)="onResolutionChange($event)"
        ></app-dropdown-form-group>
        @if (resolutionSelected === resolutionEmergency.other) {
        <app-form-group
          [label]="'vehicle.emergency.modal.comment' | translate"
          [isTextArea]="true"
          [control]="data?.comment"
          [placeholder]="
            'vehicle.emergency.modal.commentPlacehoder' | translate
          "
          controlId="comment"
          class="vehicle-model-section__input"
        ></app-form-group>
        }
      </div>
    </form>
  </div>

  <div class="action-dialog">
    <button class="btn-quaternary" (click)="onCancel()">
      {{ data?.cancelBtn }}
    </button>

    <button
      class="btn-primary btn-confirm"
      (click)="data?.emergencyFormClose?.invalid ? false : onConfirm()"
      [disabled]="data?.emergencyFormClose?.invalid"
    >
      {{ data?.submitBtn }}
    </button>
  </div>
</div>
