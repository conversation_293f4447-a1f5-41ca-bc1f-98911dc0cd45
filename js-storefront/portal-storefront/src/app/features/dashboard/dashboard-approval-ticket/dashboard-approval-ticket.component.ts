import {
  Component,
  HostListener,
  inject,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { PieChartComponent } from '../../../core/shared/pie-chart/pie-chart.component';
import { isInViewport } from '../../../core/helpers';
import { LoadingComponent } from '../../../layout/global/loading/loading.component';
import { CommonModule } from '@angular/common';
import { DashboardService, LoadingService } from '../../../core/services';
import { ChartType } from '../../../core/enums';
import { ItemChart } from '../../../core/interfaces';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard-approval-ticket',
  standalone: true,
  imports: [PieChartComponent, LoadingComponent, CommonModule],
  templateUrl: './dashboard-approval-ticket.component.html',
  styleUrl: './dashboard-approval-ticket.component.scss',
  providers: [DashboardService],
})
export class DashboardApprovalTicketComponent implements OnInit {
  @Input() index;

  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);
  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  ticketByType: ItemChart[] = [];

  ticketByTypeColors = [
    '#6765d8',
    '#2aba6c',
    '#119900',
    '#d2545f',
    '#fcbf45',
    '#de9e1c',
    '#564c9d',
    '#099ec6',
    '#dc3d97',
  ];

  ticketByStatus: ItemChart[] = [];

  ticketByStatusColors = [
    '#df6969',
    '#faa627',
    '#5c9cd8',
    '#099ec6',
    '#dc3d97',
  ];

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getApprovalTicketByType();
      this.getApprovalTicketByStatus();
    }
  }

  checkInView() {
    const box = document.querySelector(`.ticket`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getApprovalTicketByType();
      this.getApprovalTicketByStatus();
    }
  }

  getApprovalTicketByType(): void {
    this.dashboardService.fetchChartData(
      ChartType.ApprovalTicketByType,
      (items) => {
        this.ticketByType = items;
      }
    );
  }

  getApprovalTicketByStatus(): void {
    this.dashboardService.fetchChartData(ChartType.ApprovalTicketByStatus, (items) => {
      this.ticketByStatus = items;
    });
  }

  viewList(): void {
    this.router.navigate(['/tickets']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
