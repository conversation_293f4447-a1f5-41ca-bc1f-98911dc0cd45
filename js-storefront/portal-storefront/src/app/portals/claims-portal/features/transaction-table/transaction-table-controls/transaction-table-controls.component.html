<div class="pagination-container table-controls">
  <ng-container *ngIf="!isTablet">
    @if (selectedList && selectedList.length > 0) {
      @if (!isNotCreateClaim) {
        <div class="create-claim">
            @if (isValidatedMarketing) {
                <button class="btn-primary" (click)="onCombieClaim()">{{'promotions.combineCaim'| translate}}</button>
                <span>{{'promotions.totalClaimableAmount'| translate}}: <b>{{totalClaimableAmount | currency : currencyIso}}</b></span>
            } @else {
                <button class="btn-primary" (click)="onCreateClaim()">{{'promotions.createClaim'| translate}}</button>
                <span>{{'promotions.totalClaimableAmount'| translate}}: <b>{{value | currency : currencyIso}}</b></span>
            }
        </div>
      } @else { 
        <ng-container *ngTemplateOutlet="content"></ng-container>
      }

    } @else {
      <div class="pagination-info">
        {{ (totalResults === 1? 'paging.oneResult' : 'paging.resultPage') | translate : {
          currentPageStart,
          currentPageEnd,
          totalResults
        } }}
      </div>
    }
  </ng-container>
  <div *ngIf="isTablet" class="pagination-info-header">
    @if (selectedList && selectedList.length > 0) {
      @if (!isNotCreateClaim) {
        <div class="create-claim">
          @if (isValidatedMarketing) {
            <button class="btn-primary" (click)="onCombieClaim()">{{'promotions.combineCaim'| translate}}</button>
          } @else {
            <button class="btn-primary" (click)="onCreateClaim()">{{'promotions.createClaim'| translate}}</button>
          }
          <span>{{'promotions.totalClaimableAmount'| translate}}: <b>{{value | currency : currencyIso}}</b></span>
        </div>
      } @else { 
        <ng-container *ngTemplateOutlet="content"></ng-container>
      }
    } @else {
      <div class="pagination-info">
        {{ (totalResults === 1? 'paging.oneResult' : 'paging.resultPage') | translate : {
          currentPageStart,
          currentPageEnd,
          totalResults
        } }}
      </div>
    }
    <div class="pagination-options-dropdown">
      <label for="resultsPerPage" class="pagination-label">{{ 'paging.show' | translate}}</label>
      <mat-form-field appearance="outline" class="pagination-select">
        <mat-select
          panelClass="custom-overlay-panel custom-panel"
          [(ngModel)]="resultsPerPage"
          (selectionChange)="updateResultsPerPage($event?.value)"
          disableRipple
          class="custom-select"
        >
          <mat-option
            class="custom-dropdown-options"
            *ngFor="let option of resultsPerPageOptions"
            [value]="option"
          >
            {{ option }}
          </mat-option>
        </mat-select>
        <mat-icon matSuffix svgIcon="ic-down"></mat-icon>
      </mat-form-field>
    </div>
  </div>
  <app-table-action
    [isShowActionExport]="isShowActionExport"
    [resultsPerPage]="resultsPerPage"
    (changeItemPerPage)="updateResultsPerPage($event)"
    (exportData)="exportData.emit()"
  ></app-table-action>
</div>

<ng-template #content>
  <ng-content></ng-content>
</ng-template>