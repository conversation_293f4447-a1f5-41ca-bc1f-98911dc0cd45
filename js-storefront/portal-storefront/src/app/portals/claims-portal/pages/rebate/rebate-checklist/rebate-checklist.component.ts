import { CommonModule, CurrencyPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash-es';
import { DropdownFormGroupComponent, FormGroupComponent, WidgetSummaryComponent } from '../../../../../core/shared';
import { ItemWidget, OptionDropdown, PagingInfo } from '../../../../../core/interfaces';
import { UserService } from '../../../../../core/services/user';
import { filter, forkJoin, Subject, takeUntil } from 'rxjs';
import { RequestRebateGenerateChecklist, ResponseRebateClaimsList } from '../../../interfaces/rebate.interface';
import { AgGridCustomComponent } from "../../../../../core/shared/ag-grid-custom/ag-grid-custom.component";
import { ID_WIDGET_GENERAL_MODEL_TRANSACTIONS, ID_WIDGET_PUSH_MODEL_TRANSACTIONS, REBATE_CHECKLIST_WIDGETS, REBATE_CLAIM_TYPE, REBATE_CLAIMS_DEALER_WIDGETS, REBATE_CLAIMS_OTHER_WIDGETS } from '../../../constants/rebate.constant';
import { ROLES } from '../../../../../core/constants/roles.const';
import { RebateService } from '../../../services/rebate/rebate.service';
import { LoadingService, NotificationService } from '../../../../../core/services';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDialog } from '@angular/material/dialog';
import { ModalGenerateClaimComponent } from './modal-generate-claim/modal-generate-claim.component';
import { ActionModal } from '../../../../../core/enums';
import { handleErrors } from '../../../../../core/helpers';
import { LoadingComponent } from "../../../../../layout/global/loading/loading.component";

@Component({
  selector: 'app-rebate-checklist',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    DropdownFormGroupComponent,
    AgGridCustomComponent,
    WidgetSummaryComponent,
    MatExpansionModule,
    LoadingComponent
],
  providers: [
    NotificationService,
    CurrencyPipe,
  ],
  templateUrl: './rebate-checklist.component.html',
  styleUrl: './rebate-checklist.component.scss'
})
export class RebateChecklistComponent {
  filterForm = new FormGroup({
    dealer: new FormControl(''),
    month: new FormControl(''),
    year: new FormControl(''),
    claimType: new FormControl(''),
  });
  internalSalesForm = new FormGroup({
    promotions: new FormControl('') // This is setting dropdown list on UI
  });
  dealers: OptionDropdown[];
  month: OptionDropdown[] = [];
  year: OptionDropdown[] = [];
  claimType: OptionDropdown[];
  promotions: OptionDropdown[] = [];
  userService = inject(UserService);
  private destroy$ = new Subject<void>();
  translateService = inject(TranslateService);
  private rebateService = inject(RebateService);
  private notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  private currencyPipe = inject(CurrencyPipe);
  
  currentRoles: string[];
  currentRole: string;
  currentWidgets: ItemWidget[] = REBATE_CHECKLIST_WIDGETS;
  ROLES = ROLES;
  REBATE_CLAIM_TYPE = REBATE_CLAIM_TYPE;

  rowData: ResponseRebateClaimsList[];
  colDefsSummary = [];
  colDefsRebateTransaction = [];
  defaultColDef = {
    resizable: false,
  };
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  summaryData;
  internalSalesData;
  rebateTransactionData;
  isRebateTransactionExpanded = false;
  dialog = inject(MatDialog);
  selectedSummaryTab;
  currentClaimType: string;
  groups;
  isSummaryEmpty = true;

  setExpanded(check: boolean) {
    this.isRebateTransactionExpanded = check
  }

  ngOnInit(): void {
    this.userService.getUserRoles$()
    .pipe(takeUntil(this.destroy$))
    .subscribe((value) => {
      if (value) {
        this.currentRoles = value.roles || [];
        this.currentRole = this.userService.getGroupRole(this.currentRoles);
        this.getDealer();
        this.getMonth();
        this.getYear();
        this.getClaimType();
      }
    });
    this.getWidget();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getDealer() {
    this.rebateService.getRebateDealerList().subscribe((res) => {
      this.dealers = _.orderBy(res, ['name'], ['asc']);
    }, (err) => {
      this.notificationService.showError('common.generalError');
    });
  }

  getMonth() {
    for (let i = 1; i <= 12; i++) {
      this.month.push({
        code: i.toString(),
        name: String(i).padStart(2, '0'),
      });
    }
  }

  getYear() {
    // Get the year from 2019 to the current year
    const currentYear = new Date().getFullYear();
    const startYear = 2019;
    for (let i = startYear; i <= currentYear; i++) {
      this.year.push({
        code: i.toString(),
        name: i.toString(),
      });
    }
    this.year.reverse();
  }

  getClaimType() {
    this.rebateService.getRebateClaimTypeList().subscribe((res) => {
      this.claimType = res.map((item) => ({
        code: item.code,
        name: this.translateService.instant(`rebate.claims.type.${item.code}`),
      }));
    }, (err) => {
      this.notificationService.showError('common.generalError');
    });
  }

  generateChecklist() {
    this.getRebateSummaryInternalSales();
    this.getRebateTransaction();
    this.getWidget();
    this.currentClaimType = this.filterForm.get('claimType').value;
  }

  getRebateSummaryInternalSales() {
    this.loadingService.showLoader();
    const dataSend = this.filterForm.value as RequestRebateGenerateChecklist;
    this.rebateService.rebateGenerateChecklist(dataSend).subscribe((res) => {
      // If the summary has only claimType, it means that the summary is empty
      this.isSummaryEmpty = this.hasOnly(res?.summary || {}, ['claimType']);
      this.summaryData = res?.summary ? [res?.summary] : null;
      this.groups = res?.groups || [];
      this.generateInternalSales();
      this.loadingService.hideLoader();
      this.initSummaryGrid();
    }, (err) => {
      this.loadingService.hideLoader();
      this.notificationService.showError('common.generalError');
    });
  }

  getRebateTransaction() {
    this.loadingService.showLoader();
    const dataSend = this.filterForm.value as RequestRebateGenerateChecklist;
    this.rebateService.rebateGenerateChecklistTransaction(dataSend).subscribe((res) => {
      this.rebateTransactionData = res || null;
      this.loadingService.hideLoader();
      this.initRebateTransactionGrid();
    }, (err) => {
      this.loadingService.hideLoader();
      this.notificationService.showError('common.generalError');
    });
  }

  initSummaryGrid(): void {
    if (this.filterForm.get('claimType').value === REBATE_CLAIM_TYPE.PUSH_MODEL) {
      this.colDefsSummary = [
        {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.level'),
          field: 'level',
          sortable: false,
          width: 80,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.totalSale'),
          field: 'totalSale',
          sortable: false,
          width: 120,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.targets'),
          field: 'target',
          sortable: false,
          width: 100,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.gap'),
          field: 'gap',
          sortable: false,
          width: 100,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.salesRebate'),
          field: 'salesRebate',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.nenkei'),
          field: 'nenkei',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.overachievement'),
          field: 'overArchievement',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.total'),
          field: 'total',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.grm'),
          field: 'grm',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.mp'),
          field: 'mp',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.totalGrmMp'),
          field: 'totalGrmAndMp',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.totalRebate'),
          field: 'totalRebate',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        },
      ];
    }
    if (this.filterForm.get('claimType').value === REBATE_CLAIM_TYPE.GENERAL) {
      this.colDefsSummary = [
        {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.description'),
          field: 'description',
          sortable: false,
          cellRenderer: () => this.translateService.instant(`rebate.checklist.summaryGrid.summaryDescriptionValue`)
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.unitSold'),
          field: 'unitSold',
          sortable: false,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.target'),
          field: 'target',
          sortable: false,
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.achievement'),
          field: 'achievement',
          sortable: false,
          valueFormatter: (params) => {
            return params.value ? params.value + '%' : '0%';
          }
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.incentive'),
          field: 'incentive',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        }, {
          headerValueGetter: () => this.translateService.instant('rebate.checklist.summaryGrid.total'),
          field: 'total',
          sortable: false,
          valueFormatter: (params) => {
            return this.currencyPipe.transform(params.value?.value, params.value?.currencyIso);
          },
          cellClass: 'currency-with-symbol',
        },
      ]
    }
  }

  initRebateTransactionGrid(): void {
    this.colDefsRebateTransaction = [
      {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.transactionId'),
        field: 'id',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.releaseDate'),
        field: 'releaseDate',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.invoiceCreationDate'),
        field: 'invoiceCreationDate',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.billingDocument'),
        field: 'billingDocument',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.customerName'),
        field: 'customerName',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.customerAdvisor'),
        field: 'customerAdvisor',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.vin'),
        field: 'vin',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.vehicleModel'),
        field: 'vehicleModel',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.vehicleGuid'),
        field: 'vehicleGuid',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.variant'),
        field: 'variant',
        sortable: false,
      }, {
        headerValueGetter: () => this.translateService.instant('rebate.checklist.rebateTransactionGrid.modelYear'),
        field: 'modelYear',
        sortable: false,
      },
    ];
  }

  generateInternalSales() {
    // Get the group name dropdown list
    this.promotions = this.groups.map((item) => ({
      code: item.groupId,
      name: item.groupName,
    }));
    // Set the default value for the group name
    this.internalSalesForm.patchValue({
      promotions: this.promotions[0]?.code || '',
    });
    this.searchInternalSales();
  }

  searchInternalSales() {
    const groupId = this.internalSalesForm.get('promotions').value || '';
    this.internalSalesData = this.groups.find((item) => item.groupId === groupId) || null;
  }

  getWidget() {
    this.loadingService.showLoader();
    this.rebateService.getRebateChecklistWidget(
      this.filterForm.get('dealer').value,
      this.filterForm.get('month').value,
      this.filterForm.get('year').value
    ).subscribe((res) => {
      if (res) {
        this.currentWidgets.forEach((widget, index: number) => {
          res.forEach((item) => {
            if (widget.id === item.type) {
              this.currentWidgets[index].count = item.count;
            }
          });
        });
      }
      this.loadingService.hideLoader();
    }, (err) => {
      handleErrors(err, this.notificationService);
      this.loadingService.hideLoader();
    });
  }

  changeSummaryTab(id: string): void {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;

    this.mapWidgetFilter(id);
    this.generateChecklist();
  }

  mapWidgetFilter(id: string) {
    switch (id) {
      case ID_WIDGET_PUSH_MODEL_TRANSACTIONS: {
        this.filterForm.patchValue({
          claimType: ID_WIDGET_PUSH_MODEL_TRANSACTIONS,
          month: this.filterForm.get('month').value || '',
          year: this.filterForm.get('year').value || '',
        });
        break;
      }
      case ID_WIDGET_GENERAL_MODEL_TRANSACTIONS: {
        this.filterForm.patchValue({
          claimType: ID_WIDGET_GENERAL_MODEL_TRANSACTIONS,
          month: this.filterForm.get('month').value || '',
          year: this.filterForm.get('year').value || '',
        });
        break;
      }
    }
  }

  openGenerateClaim(): void {
    const claimType = this.filterForm.get('claimType').value;
    const dialogRef = this.dialog.open(ModalGenerateClaimComponent, {
      width: '600px',
      data: {
        dealer: this.filterForm.get('dealer').value,
        month: this.filterForm.get('month').value,
        year: this.filterForm.get('year').value,
        claimType: claimType,
        totalRebate: claimType === ID_WIDGET_PUSH_MODEL_TRANSACTIONS
          ? this.summaryData[0]?.totalRebate?.value || 0
          : this.summaryData[0]?.total?.value || 0,
      }
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          // Reset filter form
          this.filterForm.patchValue({
            dealer: '',
            month: '',
            year: '',
            claimType: '',
          });
          this.generateChecklist();
        }
      });
  }

  hasOnly(obj: any, props: string[]) {
    const objProps = Object.keys(obj);
    return objProps?.length === props?.length && props.every((p: string) => objProps.includes(p));
  }
}
