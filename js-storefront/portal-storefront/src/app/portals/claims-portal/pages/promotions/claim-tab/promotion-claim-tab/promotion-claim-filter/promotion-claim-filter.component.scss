@import "../../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../../styles/abstracts/variables";

:host {
  width: 100%;
  form {
    width: 100%;
    .filter-transaction {
      width: 100%;
      display: flex;
      gap: 20px;
      align-items: flex-end;
      justify-content: space-between;

      &__properties {
        display: flex;
        flex-wrap: wrap;
        gap: 0 20px;
        width: 100%;
        app-dropdown-form-group {
          &:nth-child(1),
          &:nth-child(2) {
            width: calc(20% - 17px);
          }
          &:nth-child(3),
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6) {
            flex-shrink: 0;
            width: calc(15% - 17px);
          }
        }
        &.hidden-dealer {
          app-dropdown-form-group {
            &:nth-child(1) {
              width: calc(30% - 17px);
            }
            &:nth-child(2),
            &:nth-child(3),
            &:nth-child(4),
            &:nth-child(5) {
              flex-shrink: 0;
              width: calc(17.5% - 17px);
            }
          }
        }
      }

      &__search {
        display: flex;
        align-items: end;

        .search {
            margin-top: 0;
            margin-bottom: 22px;
        }
      }
    }

    .advanced-title {
      color: $text-color-5;
      font-weight: $fw600;
      font-size: $fs14;

      width: fit-content;
      display: flex;
      align-items: center;
      gap: 5px;

      cursor: pointer;
    }
  }
}

:host ::ng-deep {
      .filter-transaction {
      &__properties {
        app-dropdown-form-group {
          .dropdown-form-group__label {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
}
