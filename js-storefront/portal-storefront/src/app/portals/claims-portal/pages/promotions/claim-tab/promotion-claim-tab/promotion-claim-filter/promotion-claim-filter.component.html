<form [formGroup]="form">
  <div class="filter-transaction">
    <div class="filter-transaction__properties" [class.hidden-dealer]="hiddenDealer">
      @if (!hiddenDealer) {
        <app-dropdown-form-group
        [label]="'promotions.filter.dealer' | translate"
        [control]="id"
        [options]="dealerList"
      ></app-dropdown-form-group>
      }
      <app-dropdown-form-group
        [label]="'promotions.filter.status' | translate"
        [control]="status"
        [options]="statusList"
      ></app-dropdown-form-group>
      <app-dropdown-form-group
        [label]="'promotions.filter.month' | translate"
        [control]="month"
        [options]="monthList"
      ></app-dropdown-form-group>
      <app-dropdown-form-group
        [label]="'promotions.filter.year' | translate"
        [control]="year"
        [options]="yearList"
        [errorMessage]="'promotions.pleaseChooseYear'"
        [isValid]="isYearValid"
      ></app-dropdown-form-group>
      <app-dropdown-form-group
        [label]="'promotions.filter.creationMonth' | translate"
        [control]="creationMonth"
        [options]="creationMonthList"
      ></app-dropdown-form-group>
      <app-dropdown-form-group
        [label]="'promotions.filter.creationYear' | translate"
        [control]="creationYear"
        [options]="creationYearList"
        [errorMessage]="'promotions.pleaseChooseCreationYear'"
        [isValid]="isCreationYearValid"
      ></app-dropdown-form-group>
    </div>
    <div class="filter-transaction__search">
      <button type="submit" class="btn-tertiary search" (click)="onSubmit()">
        {{ "common.search" | translate }}
      </button>
    </div>
  </div>
</form>
