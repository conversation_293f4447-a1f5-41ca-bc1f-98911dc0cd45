@import "../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../styles/abstracts/variables";

:host {
    .form-content {
        &.edit {
            position: relative;

            .form-change {
                .form-item {
                    max-width: calc(50% - 25px);
                    flex: 0 0 calc(50% - 25px);

                    &.large {
                        max-width: calc(50% - 25px);
                        flex: 0 0 calc(50% - 25px);
                    }
                }
            }

            .btn-block {
                @include lg {
                    right: 0;
                    top: -67px;
                }
            }
        }

        .btn-block {
            display: flex;
            gap: 20px;

            @include lg {
                position: absolute;
                right: 30px;
                top: 30px;
            }
            
            button {
                min-width: 150px;
            }
        }

        label {
            display: block;
            font-weight: $fw600;
            font-size: $fs16;
            margin-bottom: 1px;
        }

        .form-item {
            @include md-max {
                margin-bottom: 22px;
            }
        }

        .form-change {
            @include lg {
                display: flex;
                gap: 0 50px;
                flex-flow: row wrap;
            }

            .input-require {
                margin-bottom: 22px;

                .custom-input {
                    margin-bottom: 0;
                }
            }

            .type-item,
            .form-item {
                @include lg {
                    max-width: calc(100%/3 - 34px);
                    flex: 0 0 calc(100%/3 - 34px);
                }


                &.large {
                    @include lg {
                        max-width: calc(33.3333333333% * 2 - 17px);
                        flex: 0 0 calc(33.3333333333% * 2 - 17px);
                    }
                }

                &.full {
                    @include lg {
                        max-width: 100%;
                        flex: 0 0 100%;
                    }
                }
            }
        }
    }
}
