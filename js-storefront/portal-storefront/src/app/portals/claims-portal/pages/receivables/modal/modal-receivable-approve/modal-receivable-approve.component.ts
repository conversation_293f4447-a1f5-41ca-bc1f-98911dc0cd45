import { Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { ActionModal } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { ModalCloseComponent } from '../../../../../../features/vehicle/vehicle-detail/emergency-tab/modal-close/modal-close.component';
import { ReceivableService } from '../../../../services/receivable/receivable.service';

@Component({
  selector: 'app-modal-receivable-approve',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
  ],
  providers: [NotificationService, ReceivableService],
  templateUrl: './modal-receivable-approve.component.html',
  styleUrls: ['./modal-receivable-approve.component.scss'],
})
export class ModalReceivableApproveComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCloseComponent>);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  receivableService = inject(ReceivableService);
  mainSubscription = new Subscription();
  form: FormGroup;
  ngOnInit() {
    this.form = this.data.form;
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    const formData = this.data?.form?.value;
    const params = {
      glCode: formData.glCode,
      costCenter: formData.costCenter,
      internalOrderNo: formData.internalOrderNo,
      profitCenter: formData.profitCenter,
    };
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.receivableService
        .approveReceivableClaim(params, this.data.claimCode)
        .subscribe(
          (res) => {
            if (res.code === '200') {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'receivables.message.approveSuccess'
                )
              );
              this.dialogRef.close({ action: ActionModal.Submit });
            } else {
              this.notificationService.showError(res?.message);
            }
            this.loadingService.hideLoader();
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }
}
