<div class="promotion-container">
  <div class="promotion-claim-content detail-content-table">
      <app-receivable-claim-filter
      [form]="filterClaimForm"
      [isDisplayDealer]="isDisplayDealer"
      [statusList]="statusList"
      [dealerList]="dealer"
      (search)="searchClaimList()">
      </app-receivable-claim-filter>

      <app-transaction-table
      [rowData]="rowData"
      [colDefs]="colDefs"
      [isNotCreateClaim]="currentRole === ROLES.MARKETINGGROUP"
      [defaultColDef]="defaultColDef"
      [gridOptions]="gridOptions"
      [pagingInfo]="pagingInfo"
      (rowSelected)="onRowSelected($event)"
      (onPageChange)="onPageChange($event)"
      (changeItemPerPage)="changeItemPerPage($event)"
      (exportData)="exportData()"
    >
    @if (currentRole === ROLES.MARKETINGGROUP) {
      <div class="create-claim">
        <button class="btn-primary" (click)="openGenerateExcelModal(rowsSelected, true)">
          {{ "promotions.generateExcelForCAS" | translate }}
        </button>
        <span
          ><b>{{rowsSelected && rowsSelected.length}} {{ "promotions.selection" | translate }} </b></span
        >
      </div>
    }
      </app-transaction-table>
  </div>
</div>
