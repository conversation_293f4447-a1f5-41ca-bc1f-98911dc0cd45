@import "../../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../../styles/abstracts/variables";

:host {
  width: 100%;
  form {
    width: 100%;
    .filter-transaction {
      width: 100%;
      display: flex;
      gap: 20px;
      align-items: flex-end;
      justify-content: space-between;

      &__properties {
        display: flex;
        flex-wrap: wrap;
        gap: 0 20px;
        width: 100%;
        app-dropdown-form-group {
          flex: 1;
        }
      }

      &__search {
        display: flex;
        align-items: end;

        .search {
            margin-top: 0;
            margin-bottom: 22px;
        }
      }
    }

    .advanced-title {
      color: $text-color-5;
      font-weight: $fw600;
      font-size: $fs14;

      width: fit-content;
      display: flex;
      align-items: center;
      gap: 5px;

      cursor: pointer;
    }
  }
}
