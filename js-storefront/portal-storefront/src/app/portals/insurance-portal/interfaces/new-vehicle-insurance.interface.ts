import { OptionDropdown, ResponsePaging } from "../../../core/interfaces";

export interface RequestNewVehicleInsuranceList {
  vin?: string;
  dealer?: string;
  status?: string;
  month?: string;
  customerName?: string;
  currentPage?: number;
  pageSize?: number;
  ticketId?: string;
}

export interface NewVehicleInsuranceItem {
  approvalDate : string;
  creationSource : string;
  customerName : string;
  insurancePartnerName : string;
  requestedDate : string;
  status : { code : string };
  vehicle : string;
}

export interface ResponseNewVehicleInsuranceList {
  type: string;
  pagination: ResponsePaging;
  items: NewVehicleInsuranceItem[];
}

export interface FilterNewVehicleInsurance {
  statusList: OptionDropdown[];
}
