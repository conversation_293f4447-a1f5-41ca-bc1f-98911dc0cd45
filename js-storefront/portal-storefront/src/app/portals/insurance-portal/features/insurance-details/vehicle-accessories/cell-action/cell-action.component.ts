import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ActionTable } from '../../../../../../core/enums';
import { UserService } from '../../../../../../core/services/user';


@Component({
  selector: 'app-cell-action',
  standalone: true,
  imports: [IconModule, TranslateModule, CommonModule],
  templateUrl: './cell-action.component.html',
  styleUrls: ['./cell-action.component.scss'],
})
export class CellActionComponent {
  @Input() rowIndex!: number;
  @Output() remove = new EventEmitter<void>();

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  params!: any;
  isEdit: boolean = false;
  noAction: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.isEdit =
      params?.data?.status?.code === undefined ||
      params?.data?.status?.code !== 'ACTIVE'
        ? false
        : true;
    this.noAction =
      params?.data?.status?.code !== undefined &&
      params?.data?.status?.code !== 'ACTIVE'
        ? true
        : false;
    this.rowIndex = params.rowIndex;
  }

  onDelete(): void {
    this.params.onClick(ActionTable.Remove, this.params?.data);
  }

  onEdit(): void {
    this.params.onClick(ActionTable.Edit, this.params?.data);
  }
}
