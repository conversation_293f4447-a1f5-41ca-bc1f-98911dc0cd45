import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef, } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule, DatePipe } from '@angular/common';
import moment from 'moment';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DateFormat } from '../../../../../../core/enums';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { DateFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { InsuranceInformationService } from '../../../../services/Insurance-information/Insurance-information.service';
import { InsuranceInput } from '../../../../enum';


@Component({
  selector: 'app-modal-update-insurance-role-partner',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    MatDialogModule,
    DateFormGroupComponent,
    FormGroupComponent,
],
  templateUrl: './modal-update-insurance-role-partner.component.html',
  styleUrl: './modal-update-insurance-role-partner.component.scss',
  providers: [NotificationService, InsuranceInformationService, DatePipe],
})
export class ModalUpdateInsuranceRolePartnerComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter<void>();
  dateFormat = DateFormat

  dialogRef = inject(MatDialogRef<ModalUpdateInsuranceRolePartnerComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  insuranceInformationService = inject(InsuranceInformationService);
  datePipe = inject(DatePipe);
  currentDate = new Date();
  dateOfPolicyWarning = '';
  dateOfPolicyCTPLWarning = '';
  isComprehensiveCompulsoryTPL: boolean = false;

  form = new FormGroup({
    policyCode: new FormControl(''),
    dateOfPolicy: new FormControl(null),
    expireDate: new FormControl(null),
    compDateOfPolicy: new FormControl(null),
    compExpireDate: new FormControl(null),
    compPolicyCode: new FormControl(''),
    ctplDateOfPolicy: new FormControl(null),
    ctplExpireDate: new FormControl(null),
    ctplPolicyCode: new FormControl('')
  });

  ngOnInit(): void {
    this.isComprehensiveCompulsoryTPL = this.data?.isComprehensiveCompulsoryTPL;
    if (this.isComprehensiveCompulsoryTPL) {
      this.form.patchValue({
        compDateOfPolicy: this.data?.insuranceCalculation?.compData?.policyDate || null,
        compExpireDate: this.data?.insuranceCalculation?.compData?.policyExpiryDate || null,
        compPolicyCode: this.data?.insuranceCalculation?.compData?.policyCode || '',
        ctplDateOfPolicy: this.data?.insuranceCalculation?.ctplData?.policyDate || null,
        ctplExpireDate: this.data?.insuranceCalculation?.ctplData?.policyExpiryDate || null,
        ctplPolicyCode: this.data?.insuranceCalculation?.ctplData?.policyCode || ''
      })
    } else {
      this.form.patchValue({
        policyCode: this.data?.insuranceInformation?.policyCode,
        dateOfPolicy: this.data?.insuranceInformation?.dateOfPolicy ? new Date(this.data?.insuranceInformation?.dateOfPolicy) : null,
        expireDate: this.data?.insuranceInformation?.expireDate ? new Date(this.data?.insuranceInformation?.expireDate) : null,
      });
    }

    
    if (this.data.insuranceInput === InsuranceInput.NEW) {
      this.form.get('ctplExpireDate').setValidators(this.ctplExpireDateValidator.bind(this));
      this.form.get('ctplExpireDate').updateValueAndValidity();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  updateCustomerInformation(): void {
    this.loadingService.showLoader();
    if (this.isComprehensiveCompulsoryTPL) {
      const data = this.form?.value;
      const payload = {
        compDateOfPolicy: data?.compDateOfPolicy ? this.datePipe.transform(data?.compDateOfPolicy, DateFormat.ShortDate) : "",
        compExpireDate: data?.compExpireDate ? this.datePipe.transform(data?.compExpireDate, DateFormat.ShortDate) : "",
        compPolicyCode: data?.compPolicyCode,
        ctplDateOfPolicy: data?.ctplDateOfPolicy ? this.datePipe.transform(data?.ctplDateOfPolicy, DateFormat.ShortDate) : "",
        ctplExpireDate: data?.ctplExpireDate ? this.datePipe.transform(data?.ctplExpireDate, DateFormat.ShortDate) : "",
        ctplPolicyCode: data?.ctplPolicyCode
  
      };
      this.insuranceInformationService.combinePartnerUpdate(
        this.data.insuranceId,
        payload,
      ).subscribe(() => {
        this.insuranceUpdated.emit();
        this.notificationService.showSuccess(this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.updateSuccess'));
        this.dialogRef.close(true);
        this.loadingService.hideLoader();
      },
      () => {
        this.notificationService.showError(this.translateService.instant('common.generalError'));
        this.loadingService.hideLoader();
      });
    } else {
      const payload = {
        policyCode: this.form.get('policyCode').value,
        dateOfPolicy: this.datePipe.transform(this.form.get('dateOfPolicy').value, DateFormat.ShortDate),
        expireDate: this.datePipe.transform(this.form.get('expireDate').value, DateFormat.ShortDate),
      };
      this.insuranceInformationService.updateInsuranceRolePartner(
        this.data.insuranceId,
        payload,
        this.data.insuranceInput === InsuranceInput.RENEWAL
      ).subscribe(() => {
        this.insuranceUpdated.emit();
        this.notificationService.showSuccess(this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.updateSuccess'));
        this.dialogRef.close(true);
        this.loadingService.hideLoader();
      },
      () => {
        this.notificationService.showError(this.translateService.instant('common.generalError'));
        this.loadingService.hideLoader();
      });
    }
  }

  onExpireDateChange(event: any, name: string): void {
    // Expire date should be greater than current date
    if (event.toDate() < this.currentDate) {
      this.form.get(name).setErrors({ invalid: true });
    }
  }

  onDateOfPolicyChange(event: any, isCTPL: boolean = false): void {
    // if date of policy is greater than current date 30 days, system will display a warning message under this field:
    // "You’ve entered <x> days beyond current date." which x = selected day - current date
    // if date of policy is less than current date 30 days, system will display a warning message under this field:
    // "You’ve entered <x> days advance from current date." which x = current date - selected day
    const dateOfPolicy = moment(event.toDate());
    const today = moment(this.currentDate);
    const diffDays = Math.abs(dateOfPolicy.diff(today, 'days')) - 30;
    if (diffDays >= 0) {
      if (isCTPL) {
        if (dateOfPolicy.isAfter(today)) {
          this.dateOfPolicyCTPLWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyBeyond', { days: diffDays });
        } else {
          this.dateOfPolicyCTPLWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyAdvance', { days: diffDays });
        }
      } else {
        if (dateOfPolicy.isAfter(today)) {
          this.dateOfPolicyWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyBeyond', { days: diffDays });
        } else {
          this.dateOfPolicyWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyAdvance', { days: diffDays });
        }
      }

    } else {
      if (isCTPL) {
        this.dateOfPolicyCTPLWarning = '';
      } else {
        this.dateOfPolicyWarning = '';
      }
    }
    if (this.data.insuranceInput === InsuranceInput.NEW) {
      this.form.get('ctplExpireDate').updateValueAndValidity();
    }
  }
  
  ctplExpireDateValidator(control: FormControl) {
    // Show the reminder message for CTPL end date in partner view 
    // if the CTPL expiry date is less than (CTPL date of Policy Insurance + 1094 days) or greater than (CTPL  date of Policy Insurance + 1095 days)
    // 'The CTPL policy expiry date should be three years from the date of policy issuance.'
    
    const ctplDateOfPolicy = this.form.get('ctplDateOfPolicy').value;
    if (!ctplDateOfPolicy || !control.value) {
      return null;
    }

    const ctplExpireDate = moment(control.value);
    const maxDate = moment(ctplDateOfPolicy).add(3, 'years');

    if (!ctplExpireDate.isSame(maxDate)) {
      return { ctplExpireDateInvalid: true };
    }

    return null;
  }

}
