<div class="detail-box">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon
        svgIcon="ic-insurance-coverage"
        class="medium-icon section-header--icon"
      ></mat-icon>
      {{
        "newVehicleInsurance.detail.insuranceInformation.sectionName"
          | translate
      }}
    </div>
    @if (isEditable) {
    <button class="btn-link" (click)="openEditDialog()">
      <mat-icon
        svgIcon="ic-edit"
        class="small-icon"
        aria-hidden="true"
      ></mat-icon>
      {{ "common.edit" | translate }}
    </button>
    }
  </div>
  <div class="detail-content">
    @if (currentRole === ROLES.COORDINATORGROUP
      || (currentRole === ROLES.INSURANCEDEPARTMENTGROUP && insuranceInput === INSURANCE_INPUT.RENEWAL)) {
      @if (insuranceInput === INSURANCE_INPUT.CREATE) {
        <div class="detail-row">
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insurancePolicyCode" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.policyCode || '-' }}</div>
          </div>
          @if (!isComprehensiveCompulsoryTPL) {
            <div class="detail-element">
              <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceExpiryDate" | translate }}</div>
                <div class="detail-value">{{ insurancePartnerInformation?.expireDate || '-' }}</div>
            </div>
          }
        </div>
        <div class="detail-row">
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCompany" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.insuranceCompany || '-' }}</div>
          </div>
        </div>
      } @else if (currentRole === ROLES.COORDINATORGROUP
        && insuranceStatus === INSURANCE_STATUS.COMPLETED
        && (insuranceInput === INSURANCE_INPUT.RENEWAL || insuranceInput === INSURANCE_INPUT.NEW)) {
        <div class="detail-row">
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceType" | translate }}</div>
              <div class="detail-value">
                {{
                  !insurancePartnerInformation?.insuranceType
                  ? '-'
                  : "insurance.insuranceType." + insurancePartnerInformation?.insuranceType | translate
                }}
              </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCompany" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.insuranceCompany || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insurancePolicyCode" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.policyCode || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction.name" | translate }}</div>
            <div class="detail-value">
              {{
                !insuranceInformation?.transactionType
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction." + insuranceInformation?.transactionType | translate
              }}
            </div>
          </div>
          @if(!isComprehensiveCompulsoryTPL) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.dateOfPolicyInsurance" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.dateOfPolicy || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceExpiryDate" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.expireDate || '-' }}</div>
          </div>
          }
        </div>
        <div class="detail-row">
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage.name" | translate }}</div>
            <div class="detail-value">
              {{
                !insuranceInformation?.insuranceCoverage
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage." + insuranceInformation?.insuranceCoverage | translate
              }}
            </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.scheme" | translate }}</div>
            <div class="detail-value">
              {{
                insuranceInformation?.scheme && scheme
                  ? scheme[insuranceInformation?.scheme]
                  : "-"
              }}
            </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCost" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.insuranceCost?.value || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance.name" | translate }}</div>
            <div class="detail-value">
              {{
                !insuranceInformation?.typeOfInsurance
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance." + insuranceInformation?.typeOfInsurance | translate
              }}
            </div>
          </div>
          <div class="detail-element"></div>
          <div class="detail-element"></div>
        </div>
      } @else {
        <div class="detail-row">
          <div class="detail-element">
            <div class="detail-label">
              {{
                "newVehicleInsurance.detail.insuranceInformation.scheme" | translate
              }}
            </div>
            <div class="detail-value">
              {{
                insuranceInformation?.scheme && scheme
                  ? scheme[insuranceInformation?.scheme]
                  : "-"
              }}
            </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">
              {{
                "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction.name"
                  | translate
              }}
            </div>
            <div class="detail-value">
              {{
                !insuranceInformation?.transactionType
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction." + insuranceInformation?.transactionType | translate
              }}
            </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">
              {{
                "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance.name"
                  | translate
              }}
            </div>
            <div class="detail-value">
              {{
                !insuranceInformation?.typeOfInsurance
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance." + insuranceInformation?.typeOfInsurance | translate
              }}
            </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">
              {{
                "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage.name"
                  | translate
              }}
            </div>
            <div class="detail-value">
              {{
                !insuranceInformation?.insuranceCoverage
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage." + insuranceInformation?.insuranceCoverage | translate
              }}
            </div>
          </div>
        </div>
      }
    }

    @if (currentRole === ROLES.INSURANCEPARTNERGROUP
      || (currentRole === ROLES.INSURANCEADMINGROUP)
      || (currentRole === ROLES.INSURANCEDEPARTMENTGROUP && insuranceInput === INSURANCE_INPUT.NEW)) {
      <div class="detail-row">
        @if (!isComprehensiveCompulsoryTPL) {
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceType" | translate }}</div>
            <div class="detail-value">
              {{
                !insurancePartnerInformation?.insuranceType
                ? '-'
                : "insurance.insuranceType." + insurancePartnerInformation?.insuranceType | translate
              }}
            </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCompany" | translate }}</div>
            <div class="detail-value">{{ insurancePartnerInformation?.insuranceCompany || '-' }}</div>
        </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insurancePolicyCode" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.policyCode || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCost" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.insuranceCost?.value || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.dateOfPolicyInsurance" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.dateOfPolicy || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceExpiryDate" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.expireDate || '-' }}</div>
          </div>
        }
      </div>
      <div class="detail-row grid-6">
        @if (isComprehensiveCompulsoryTPL) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceType" | translate }}</div>
              <div class="detail-value">
                {{
                  !insurancePartnerInformation?.insuranceType
                  ? '-'
                  : "insurance.insuranceType." + insurancePartnerInformation?.insuranceType | translate
                }}
              </div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCompany" | translate }}</div>
              <div class="detail-value">{{ insurancePartnerInformation?.insuranceCompany || '-' }}</div>
          </div>
        }
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage.name" | translate }}</div>
          <div class="detail-value">
            {{
              !insuranceInformation?.insuranceCoverage
              ? "-"
              : "newVehicleInsurance.detail.insuranceInformation.insuranceCoverage." + insuranceInformation?.insuranceCoverage | translate
            }}
          </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.scheme" | translate }}</div>
          <div class="detail-value">
            {{
              insuranceInformation?.scheme && scheme
                ? scheme[insuranceInformation?.scheme]
                : "-"
            }}
          </div>
        </div>
        @if (currentRole === ROLES.INSURANCEADMINGROUP && insuranceInput === INSURANCE_INPUT.RENEWAL) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance.name" | translate }}</div>
            <div class="detail-value">
              {{
                !insuranceInformation?.typeOfInsurance
                ? "-"
                : "newVehicleInsurance.detail.insuranceInformation.typeOfInsurance." + insuranceInformation?.typeOfInsurance | translate
              }}
            </div>
          </div>
        }
        @if (currentRole !== ROLES.INSURANCEADMINGROUP && currentRole !== ROLES.INSURANCEDEPARTMENTGROUP) {
          @if (insurancePartnerInformation?.type === this.insuranceType.TELEMATICS) {
            <div class="detail-element">
              <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.consentDate" | translate }}</div>
              <div class="detail-value">{{ convertUTCtoDisplayDate(insurancePartnerInformation?.consentDate) }}</div>
            </div>
            @if (insuranceInput === INSURANCE_INPUT.RENEWAL) {
              <div class="detail-element">
                <div class="detail-label">{{ "insurance.modal.drivingScore" | translate }}</div>
                <div class="detail-value">{{ insurancePartnerInformation?.drivingScore || '-' }}</div>
              </div>
              <div class="detail-element">
                <div class="detail-label">{{ "insurance.modal.telematicsDiscount" | translate }}</div>
                <div class="detail-value">{{ insurancePartnerInformation?.telematicDiscount?.value || '-' }}</div>
              </div>
            }
          }
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.startDate" | translate }}</div>
            <div class="detail-value">{{ (insurancePartnerInformation?.startDate | date: dateFormat.ShortDate) || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgage" | translate }}</div>
            <div class="detail-value">{{ insurancePartnerInformation?.mortgage?.name || '-' }}</div>
          </div>
          <div class="detail-element grid-last-item">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgageAddress" | translate }}</div>
            <div class="detail-value">{{ insurancePartnerInformation?.mortgageAddress || '-' }}</div>
          </div>
        } @else {
          @if ((currentRole === ROLES.INSURANCEADMINGROUP)
            || (currentRole === ROLES.INSURANCEDEPARTMENTGROUP && insuranceInput === INSURANCE_INPUT.NEW)) {
            <div class="detail-element">
              <div class="detail-label">
                {{ "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction.name" | translate }}
              </div>
              <div class="detail-value"> {{
                  !insuranceInformation?.transactionType
                  ? "-"
                  : "newVehicleInsurance.detail.insuranceInformation.typeOfTransaction." + insuranceInformation?.transactionType | translate
                }}
              </div>
            </div>
            }
        }
      </div>
      @if (insurancePartnerInformation?.type === this.insuranceType.TELEMATICS && !this.isComprehensiveCompulsoryTPL) {
        <div class="detail-row">
          <div class="detail-element">
            <app-file-list
              [fileList]="insurancePartnerInformation?.uploadInsuranceForm ? [insurancePartnerInformation?.uploadInsuranceForm] : []"
              [isHiddenRemove]="true"
              (handleDownload)="onDownloadFile($event)"
            ></app-file-list>
          </div>
          <div class="detail-element"></div>
          <div class="detail-element"></div>
        </div>
      }
    }
  </div>
  @if (isComprehensiveCompulsoryTPL) {
    <hr class="line-break" />
    <div class="detail-content">
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.compInsuranceNo" | translate }}</div>
            <div class="detail-value">
              {{
                insuranceCalculation?.compData?.code || '-'
              }}
            </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.compInsurancePolicyCode" | translate }}</div>
            <div class="detail-value">{{ insuranceCalculation?.compData?.policyCode || '-' }}</div>
        </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.compInsuranceCost" | translate }}</div>
              <div class="detail-value">{{ insuranceCalculation?.compData?.insuranceCost?.value || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.compDateofPolicyInsurance" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insuranceCalculation?.compData?.policyDate) }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.compInsuranceExpiryDate" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insuranceCalculation?.compData?.policyExpiryDate) }}</div>
          </div>
      </div>
      <div class="detail-row-box">
        @if (
            (!insuranceCalculation?.compData?.EPolicyDocs || 
             insuranceCalculation?.compData?.EPolicyDocs?.length === 0) || 
            (insuranceCalculation?.compData?.EPolicyDocs?.length > 0 && 
             currentRole === ROLES.COORDINATORGROUP)
          ) {
            <div class="detail-element-header mb-1">
              <label class="form-group__label">
                {{ 'newVehicleInsurance.detail.insuranceInformation.compEPolicyDocument' | translate }}
              </label>
            </div>
          }
        <div class="detail-element-box">
          @if (currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP) {
            <div class="detail-element-upload">
            @if (insuranceCalculation?.compData?.EPolicyDocs?.length > 0) {
                <div class="detail-element-header">
                    <label class="form-group__label">{{ 'newVehicleInsurance.detail.insuranceInformation.compEPolicyDocument' | translate }}</label>
                </div>
            }
                <app-upload-file
                [placeholder]="'uploadFile.selectFile' | translate"
                [multiple]="true"
                [disabled]="insuranceStatus === INSURANCE_STATUS.CANCELLED"
                [isLabelHidden]="true"
                (filesSelected)="onFileSelected($event, insuranceCalculation?.compData?.code, insuranceCalculation?.compData?.EPolicyDocs)"
                ></app-upload-file>
            </div>
          }
          <div class="detail-element-file" [ngClass]="
          {'max-w-450': (currentRole === ROLES.COORDINATORGROUP || currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP)
          }">
            @if (!insuranceCalculation?.compData?.EPolicyDocs || insuranceCalculation?.compData?.EPolicyDocs?.length === 0) {
              <div class="no-attachment">{{ 'uploadFile.noAttachedDocuments' | translate }}</div>
            } @else {
              @if(showRelease(insuranceCalculation?.compData?.alreadyReleased, insuranceCalculation?.compData?.EPolicyDocs) && (currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP)) {
                <div class="detail-element-header">
                    <label class="acion-release" (click)="onReleaseInsurance(insuranceCalculation?.compData?.code, true)">{{'newVehicleInsurance.detail.releaseEpolicy' | translate}}</label>
                </div>
              }
              <app-file-list
                [fileList]="insuranceCalculation?.compData?.EPolicyDocs"
                [isHiddenRemove]="insuranceStatus === INSURANCE_STATUS.CANCELLED || currentRole === ROLES.COORDINATORGROUP"
                (handleDelete)="onRemoveFile($event)"
                (handleDownload)="onDownloadFile($event)"
              ></app-file-list>
            }
          </div>
        </div>
      </div>
    </div>

    <hr class="line-break" />
    <div class="detail-content">
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.ctplInsuranceNo" | translate }}</div>
            <div class="detail-value">
              {{
                insuranceCalculation?.ctplData?.code || '-'
              }}
            </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.ctplInsurancePolicyCode" | translate }}</div>
            <div class="detail-value">{{ insuranceCalculation?.ctplData?.policyCode || '-' }}</div>
        </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.ctplInsuranceCost" | translate }}</div>
              <div class="detail-value">{{ insuranceCalculation?.ctplData?.insuranceCost?.value || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.ctplDateofPolicyInsurance" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insuranceCalculation?.ctplData?.policyDate) }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insuranceInformation.ctplInsuranceExpiryDate" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insuranceCalculation?.ctplData?.policyExpiryDate) }}</div>
          </div>
      </div>
      <div class="detail-row-box">
        @if ( 
        (!insuranceCalculation?.ctplData?.EPolicyDocs || insuranceCalculation?.ctplData?.EPolicyDocs?.length === 0)
        || (insuranceCalculation?.ctplData?.EPolicyDocs?.length > 0 && 
        currentRole === ROLES.COORDINATORGROUP) 
        ){
            <div class="detail-element-header mb-1">
                <label class="form-group__label">{{ 'newVehicleInsurance.detail.insuranceInformation.ctplEPolicyDocument' | translate }}</label>
            </div>
        }
        <div class="detail-element-box">
            @if (currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP) {
            <div class="detail-element-upload">
                @if (insuranceCalculation?.ctplData?.EPolicyDocs?.length > 0) {
                    <div class="detail-element-header">
                        <label class="form-group__label">{{ 'newVehicleInsurance.detail.insuranceInformation.ctplEPolicyDocument' | translate }}</label>
                    </div>
                }
                    <app-upload-file
                    [placeholder]="'uploadFile.selectFile' | translate"
                    [multiple]="true"
                    [disabled]="insuranceStatus === INSURANCE_STATUS.CANCELLED"
                    [isLabelHidden]="true"
                    (filesSelected)="onFileSelected($event, insuranceCalculation?.ctplData?.code, insuranceCalculation?.ctplData?.EPolicyDocs)"
                    ></app-upload-file>
            </div>
          }
          <div class="detail-element-file" [ngClass]="
          {'max-w-450': (currentRole === ROLES.COORDINATORGROUP || currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP)
          }">
            @if (!insuranceCalculation?.ctplData?.EPolicyDocs || insuranceCalculation?.ctplData?.EPolicyDocs?.length === 0) {
              <div class="no-attachment">{{ 'uploadFile.noAttachedDocuments' | translate }}</div>
            } @else {
              @if(showRelease(insuranceCalculation?.ctplData?.alreadyReleased, insuranceCalculation?.ctplData?.EPolicyDocs) && (currentRole === ROLES.INSURANCEPARTNERGROUP || currentRole === ROLES.INSURANCEADMINGROUP) ) {
                <div class="detail-element-header">
                        <label class="acion-release" (click)="onReleaseInsurance(insuranceCalculation?.ctplData?.code)">{{'newVehicleInsurance.detail.releaseEpolicy' | translate}}</label>
                </div>
              }
              <app-file-list
                [fileList]="insuranceCalculation?.ctplData?.EPolicyDocs"
                [isHiddenRemove]="insuranceStatus === INSURANCE_STATUS.CANCELLED || currentRole === ROLES.COORDINATORGROUP"
                (handleDelete)="onRemoveFile($event)"
                (handleDownload)="onDownloadFile($event)"
              ></app-file-list>
            }
          </div>
        </div>
      </div>
    </div>
  }
</div>
