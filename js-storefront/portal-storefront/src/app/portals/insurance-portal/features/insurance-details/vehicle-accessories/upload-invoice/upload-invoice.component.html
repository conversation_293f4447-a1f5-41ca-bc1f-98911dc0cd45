<div class="detail-box-header mb-1">
  <div class="detail-box-title">
    @if (isReadonly) {
        {{ "vehicleAccessories.invoice" | translate }}
    } @else {
        {{ "vehicleAccessories.uploadInvoice" | translate }}
    }
  </div>
</div>
<div class="detail-content">
  @if (!isReadonly) {
    <app-upload-file
        [label]="'newVehicleInsurance.detail.documents' | translate"
        [placeholder]="'uploadFile.selectFile' | translate"
        [multiple]="true"
        [accept]="'application/pdf'"
        (filesSelected)="onFileSelected($event)"
    ></app-upload-file>
    
    <!-- Error message -->
    <div *ngIf="showError" class="error-message">
        {{ 'vehicleAccessories.uploadInvoiceRequired' | translate }}
    </div>
  }
  @if(documents && documents.length && isReadonly) {
    <app-file-list [fileList]="documents" [isHiddenRemove]="isReadonly"
        (handleDelete)="onRemoveFile($event)" (handleDownload)="onDownloadFile($event)"></app-file-list>
  } @else if (documents && documents.length) {
    <app-file-list [fileList]="documents" (handleDelete)="onRemoveFile($event)" 
    (handleDownload)="onDownloadFile($event)"></app-file-list>
  }
  @else { 
    <div class="no-data">
        {{ 'vehicleAccessories.uploadFile.noData' | translate}}
    </div>
  }
</div>
