import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule, DatePipe } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { IconModule } from '../../../../../../core/icon/icon.module';
import {
  NotificationService,
  LoadingService,
  VehicleAccessoriesService,
} from '../../../../../../core/services';
import {
  DateFormGroupComponent,
  FormGroupComponent,
} from '../../../../../../core/shared';
import { InsuranceInformationService } from '../../../../services/Insurance-information/Insurance-information.service';
import { tap, catchError, of, Observable } from 'rxjs';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-modal-vehicle-accessories',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    DateFormGroupComponent,
    FormGroupComponent,
    MatDialogModule,
    MatCheckboxModule,
  ],
  templateUrl: './modal-vehicle-accessories.component.html',
  styleUrl: './modal-vehicle-accessories.component.scss',
  providers: [NotificationService, InsuranceInformationService, DatePipe],
})
export class ModalVehicleAccessoriesComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter<void>();

  dialogRef = inject(MatDialogRef<ModalVehicleAccessoriesComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  insuranceInformationService = inject(InsuranceInformationService);
  private vehicleAccessoriesService = inject(VehicleAccessoriesService);

  form: FormGroup;

  ngOnInit(): void {
    if (this.data.status === 'edit' && this.data.selectedVehicleAccessory) {
      const accessory = this.data.selectedVehicleAccessory;
      this.form = new FormGroup({
        item: new FormControl(
          { value: accessory.item, disabled: true },
          Validators.required
        ),
        srp: new FormControl(
          { value: accessory.srp.value, disabled: true },
          Validators.required
        ),
        coverageAmount: new FormControl(
          accessory.coverage?.value !== undefined
            ? accessory.coverage.value
            : '',
         [Validators.required, Validators.pattern(/^$|^[1-9]\d*(\.\d+)?$/)] 
        ),
        quantity: new FormControl(
          { value: accessory.quantity, disabled: true },
          Validators.required
        ),
        approved: new FormControl(
            accessory.approved === 'APPROVED',
            Validators.required
        ),
      });
    } else {
      this.form = new FormGroup({
        item: new FormControl('', Validators.required),
        srp: new FormControl('', [Validators.required, Validators.pattern(/^$|^[1-9]\d*(\.\d+)?$/)]),
        quantity: new FormControl('', [Validators.required, Validators.pattern(/^$|^[1-9]\d*$/)]),
      });
    }
  }

  get itemControl(): FormControl {
    return this.form.get('item') as FormControl;
  }

  get srpControl(): FormControl {
    return this.form.get('srp') as FormControl;
  }

  get quantityControl(): FormControl {
    return this.form.get('quantity') as FormControl;
  }

  get coverageAmountControl(): FormControl {
    return this.form.get('coverageAmount') as FormControl;
  }

  get isReadonly() {
    return this.data.status === 'edit';
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  validateInput(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    const currentValue = input.value;
    const key = event.key;

    if (
      ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab', 'Delete'].includes(key)
    ) {
      return;
    }

    // Allow only digits (0-9) and prevent leading zero
    if (!/^[0-9]$/.test(key)) {
      event.preventDefault();
      return;
    }

    if (currentValue.length === 0 && key === '0') {
      event.preventDefault();
    }
  }

  validateInputDecimal(event: KeyboardEvent) {
    const allowedChars = /^[0-9.]$/;
    const inputElement = event.target as HTMLInputElement;
    const currentValue = inputElement.value;
    const key = event.key;

    // Prevent multiple decimal points
    if (key === '.' && inputElement.value.includes('.')) {
      event.preventDefault();
      return;
    }

    if (currentValue.length === 0 && (key === '0' || key === '.')) {
      event.preventDefault();
    }

    if (!allowedChars.test(event.key)) {
      event.preventDefault();
    }
  }

  onPaste(event: ClipboardEvent): void {
    const pastedText = event.clipboardData?.getData('text') ?? '';

    // Check if the pasted text is a valid number
    if (!/^[1-9]\d*$/.test(pastedText)) {
      event.preventDefault(); // Prevent the paste action
    }
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    if (this.data.status === 'edit') {
      this.editVehicleAccessories();
    } else {
      this.createVehicleAccessories();
    }
  }

  editVehicleAccessories(): void {
    this.loadingService.showLoader();

    const accessory = this.data.selectedVehicleAccessory;

    const payload = {
      id: accessory.id,
      name: this.itemControl.value,
      quantity: Number(this.quantityControl.value),
      srp: Number(this.srpControl.value),
      coverage: Number(this.coverageAmountControl.value),
      status: this.form.get('approved').value ? 'APPROVED' : 'REJECTED',
    };

    this.vehicleAccessoriesService
      .editVehicleAccessories(
        this.data.insuranceCode,
        accessory.id,
        payload
      )
      .pipe(
        tap((response: any) => {
          this.loadingService.hideLoader();
          if (response.code === 'SUCCESS') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'vehicleAccessories.modal.successEditVehicleAccessories'
              )
            );
            this.dialogRef.close({
              action: 'Submit',
              data: payload,
            });
            this.insuranceUpdated.emit();
          } else {
            this.notificationService.showError(
              response.message || 'Failed to update vehicle accessory'
            );
            this.dialogRef.close({ action: 'Error' });
          }
        }),
        catchError((error) => {
          this.notificationService.showError(
            this.translateService.instant(error.message)
          );
          this.loadingService.hideLoader();
          return of(null);
        })
      )
      .subscribe();
  }

  createVehicleAccessories(): void {
    this.loadingService.showLoader();
    const payload = {
      name: this.itemControl.value,
      srp: this.srpControl.value,
      quantity: this.quantityControl.value,
    };

    this.vehicleAccessoriesService
      .createVehicleAccessories(this.data.insuranceCode, payload)
      .pipe(
        tap((response: any) => {
          this.loadingService.hideLoader();
          if (response.code === 'SUCCESS') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'vehicleAccessories.modal.successCreateVehicleAccessories'
              )
            );
            this.dialogRef.close({ action: 'Submit', data: payload });
            this.insuranceUpdated.emit();
          } else {
            this.notificationService.showError(
              response.message || 'Failed to add accessories'
            );
            this.dialogRef.close({ action: 'Error' });
          }
        }),
        catchError((error) => {
          this.notificationService.showError(
            this.translateService.instant(error.message)
          );
          this.loadingService.hideLoader();
          return of(null);
        })
      )
      .subscribe();
  }
}
