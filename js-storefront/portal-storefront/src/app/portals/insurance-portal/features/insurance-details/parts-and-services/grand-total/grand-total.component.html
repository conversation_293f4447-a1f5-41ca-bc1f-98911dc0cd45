@if (data && data.length) {
  <table class="grand-total">
    <tbody>
      @for (item of data; track item; let i = $index) { @if ((data.length - 1)
      !== i) {
      <tr class="{{item?.class}}">
        <td>{{ item?.name | translate }}:</td>
        <td>
          <app-currency
            [amount]="item?.value?.value"
            [currencyIso]="item?.value?.currencyIso"
          ></app-currency>
        </td>
      </tr>
      } @else {
      <tr class="grand-total-final">
        <td>{{ item?.name | translate }}:</td>
        <td>
          <app-currency
            [amount]="item?.value?.value"
            [currencyIso]="item?.value?.currencyIso"
          ></app-currency>
        </td>
      </tr>
      @if(disclaimerText !== '') {
        <tr> <td colspan="2" class="disclaimer-text">{{disclaimerText}}</td></tr>
      }
      } }
    </tbody>
  </table>
}

