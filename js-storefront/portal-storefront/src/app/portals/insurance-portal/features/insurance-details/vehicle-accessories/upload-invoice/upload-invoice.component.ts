import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import _ from 'lodash-es';
import { Documents } from '../../../../interfaces';
import { FileListComponent } from '../../../file-list/file-list.component';
import { UploadFileComponent } from '../../../upload-file/upload-file.component';
import { NotificationService } from '../../../../../../core/services';
import { downloadFile } from '../../../../../../core/helpers/function-common.helper';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-upload-invoice',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    UploadFileComponent,
    FileListComponent,
    CommonModule,
  ],
  providers: [NotificationService],
  templateUrl: './upload-invoice.component.html',
  styleUrl: './upload-invoice.component.scss',
})
export class UploadInvoiceComponent {
  @Input() documents: Documents[] = [];
  @Input() isRequired: boolean = false;
  @Input() isReadonly: boolean = false;
  @Output() handleSelectedFiles = new EventEmitter();
  @Output() handleDownload = new EventEmitter<Documents>();
  @Output() handleDelete = new EventEmitter<any>();
  @Output() validityChanged = new EventEmitter<boolean>();
  @Output() documentsEmpty = new EventEmitter<boolean>();
  
  showError: boolean = false;
  private dialog = inject(MatDialog);
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService);

  ngOnChanges() {
    this.checkValidity();
  }

  onFileSelected(event: FileList): void {
    if (!event || event.length === 0) return;

    const filesArray = Array.from(event);
    const currentDocuments = this.documents ?? [];

    if (currentDocuments.length + filesArray.length > 3) {
      this.notificationService.showError(
        this.translateService.instant('vehicleAccessories.uploadFile.maxFiles3')
      );
      return;
    }

    const invalidFiles = this.validateFiles(filesArray);
    if (invalidFiles) return;

    this.handleSelectedFiles.emit(event);
    this.checkValidity();
  }

  onRemoveFile(e: Documents) {
    this.handleDelete.emit({ file: e });
    this.checkValidity();
  }

  private validateFiles(files: File[]): boolean {
    for (const file of files) {
      if (file.type !== 'application/pdf') {
        this.notificationService.showError(
          this.translateService.instant(
            'vehicleAccessories.uploadFile.invalidTypePDF'
          )
        );
        return true;
      }

      if (file.size > 5 * 1024 * 1024) {
        this.notificationService.showError(
          this.translateService.instant('uploadFile.invalidLimit5MB')
        );
        return true;
      }
    }
    return false;
  }

  onDownloadFile(file: Documents) {
    downloadFile(file.realFileName, file.downloadUrl);
  }

  private checkValidity() {
    const isDocumentEmpty = _.isEmpty(this.documents);
    const isValid =
      (!this.isRequired || !isDocumentEmpty) && !this.isReadonly;
    this.showError = !isValid && this.isRequired;
    this.validityChanged.emit(isValid);
    this.documentsEmpty.emit(isDocumentEmpty);

    if (this.showError) {
      this.notificationService.showError(
        this.translateService.instant(
          'vehicleAccessories.uploadInvoiceRequired'
        )
      );
    }
  }
}
