import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash-es';
import { DateFormat } from '../../../../../../core/enums';
import { DateTimeHelper } from '../../../../../../core/helpers/date-time.helper';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { AddressService } from '../../../../../../core/services/address/address.service';
import { UserService } from '../../../../../../core/services/user';
import { DropdownFormGroupComponent, FormGroupComponent, DateFormGroupComponent } from '../../../../../../core/shared';
import { RequestUpdateCustomerDetail } from '../../../../interfaces';
import { NewVehicleInsuranceDetailService } from '../../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';


@Component({
  selector: 'app-customer-information-update',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
    MatDialogModule,
    DateFormGroupComponent
],
  templateUrl: './customer-information-update.component.html',
  styleUrl: './customer-information-update.component.scss',
  providers: [
    NotificationService,
    DatePipe,
    DateTimeHelper
  ],
})
export class CustomerInformationUpdateComponent implements OnInit {
  dialogRef = inject(MatDialogRef<CustomerInformationUpdateComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  userService = inject(UserService);
  newVehicleInsuranceDetailService = inject(NewVehicleInsuranceDetailService);
  addressService = inject(AddressService);
  datePipe = inject(DatePipe);
  private dateTimeHelper = inject(DateTimeHelper);

  form = new FormGroup({
    firstName: new FormControl('', Validators.required),
    middleName: new FormControl('', Validators.required),
    lastName: new FormControl('', Validators.required),
    birthday: new FormControl(null, Validators.required),
    phone: new FormControl('', Validators.required),
    email: new FormControl('', Validators.required),
    titles: new FormControl('', Validators.required),
    genders: new FormControl('', Validators.required),
    streetNumber: new FormControl('', Validators.required),
    apartment: new FormControl(''),
    streetName: new FormControl(''),
    village: new FormControl(''),
    region: new FormControl('', Validators.required),
    province: new FormControl('', Validators.required),
    city: new FormControl('', Validators.required),
    barangay: new FormControl('', Validators.required),
    zipCode: new FormControl('', Validators.required),
  });

  titlesOption: OptionDropdown[];
  gendersOption: OptionDropdown[];
  regionOption: OptionDropdown[];
  provinceOption: OptionDropdown[];
  cityOption: OptionDropdown[];
  barangayOption: OptionDropdown[];
  zipCodeOption: OptionDropdown[];
  customerAddress;

  titleGenderMap;
  ngOnInit(): void {
    this.getTitles();
    this.getRegion();
    this.getCustomerDetail(this.data.uid);

    this.form.get('region').valueChanges.subscribe((regionCode) => {
      if (regionCode) {
        // Clear province, city, barangay, and zip code
        this.provinceOption = [];
        this.cityOption = [];
        this.barangayOption = [];
        this.zipCodeOption = [];
        this.getProvinceByRegion(regionCode);
      }
    });

    this.form.get('province').valueChanges.subscribe((provinceCode) => {
      if (provinceCode) {
        // Clear city, barangay, and zip code
        this.cityOption = [];
        this.barangayOption = [];
        this.zipCodeOption = [];
        this.getCitiesByProvince(provinceCode);
        this.getZipcodeByProvince(provinceCode);
      }
    });

    this.form.get('city').valueChanges.subscribe((cityCode) => {
      if (cityCode) {
        // Clear barangay
        this.barangayOption = [];
        this.getBarangayByCity(cityCode);
      }
    });

    // whenever titles change, the genders should be changed by titleGenderMap
    this.form.get('titles').valueChanges.subscribe((titleCode) => {
      if (titleCode) {
        // set gender
        this.form.get('genders').patchValue(this.titleGenderMap.find((item) => item?.key === titleCode)?.value?.value);
      }
    })
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getTitles(): void {
    this.loadingService.showLoader();
    this.userService.getUserTitles().subscribe((response) => {
      this.gendersOption = response?.genders;
      this.titlesOption = response?.titles;
      this.titleGenderMap = response?.titleGenderMap;
    },
    (err) => {
      this.loadingService.hideLoader();
    });
  }

  getCustomerDetail(uid: string): void {
    this.loadingService.showLoader();
    this.data.getCustomerInformation(uid).subscribe((response: any) => {
      this.form.patchValue(response);
      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  getRegion(): void {
    this.loadingService.showLoader();
    this.addressService.getRegionList().subscribe((response) => {
      this.regionOption = response?.regions.map((item) => ({
        code: item.isocode,
        name: item.name,
      }));
      this.regionOption = _.union(this.regionOption, [{
        code: this.customerAddress?.region?.isocode,
        name: this.customerAddress?.region?.name,
      }]);
      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  getProvinceByRegion(regionCode: string): void {
    this.loadingService.showLoader();
    this.addressService.getProvinceByRegion(regionCode).subscribe((response) => {
      this.provinceOption = response?.provinces.map((item) => ({
        code: item.isocode,
        name: item.name,
      }));
      this.provinceOption = _.union(this.provinceOption, [{
        code: this.customerAddress?.province?.isocode,
        name: this.customerAddress?.province?.name,
      }]);

      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  getCitiesByProvince(provinceCode: string): void {
    this.loadingService.showLoader();
    this.addressService.getCitiesByProvince(provinceCode).subscribe((response) => {
      this.cityOption = response?.cities.map((item) => ({
        code: item.isocode,
        name: item.name,
      }));
      this.cityOption = _.union(this.cityOption, [{
        code: this.customerAddress?.city?.isocode,
        name: this.customerAddress?.city?.name,
      }]);
      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  getBarangayByCity(cityCode: string): void {
    this.loadingService.showLoader();
    this.addressService.getBarangaysByCity(cityCode).subscribe((response) => {
      this.barangayOption = response?.barangays.map((item) => ({
        code: item.isocode,
        name: item.name,
      }));
      this.barangayOption = _.union(this.barangayOption, [{
        code: this.customerAddress?.barangay?.isocode,
        name: this.customerAddress?.barangay?.name,
      }]);
      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  getZipcodeByProvince(provinceCode: string): void {
    this.loadingService.showLoader();
    this.addressService.getZipCodesByProvince(provinceCode).subscribe((response) => {
      this.zipCodeOption = response?.zipCodes.map((item) => ({
        code: item.code,
        name: item.name,
      }));
      this.zipCodeOption = _.union(this.zipCodeOption, [{
        code: this.customerAddress?.postalCode,
        name: this.customerAddress?.postalCode,
      }]);
      this.loadingService.hideLoader();
    },
    (err) => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  updateCustomerInformation(): void {
    this.loadingService.showLoader();
    this.data.saveCustomerInformation(this.data.insuranceId, this.form.value).subscribe(() => {
      this.notificationService.showSuccess(this.translateService.instant('newVehicleInsurance.detail.customerInformation.updateSuccess'));
      this.dialogRef.close(true);
      this.loadingService.hideLoader();
    },
    () => {
      this.notificationService.showError(this.translateService.instant('common.generalError'));
      this.loadingService.hideLoader();
    });
  }

  onChangeOption(e: any, type) {
    switch(type) {
      case 'region': 
        this.form.get('province').patchValue('');
        this.form.get('city').patchValue('');
        this.form.get('barangay').patchValue('');
        this.form.get('zipCode').patchValue('');
        break;
      case 'province': 
        this.form.get('city').patchValue('');
        this.form.get('barangay').patchValue('');
        this.form.get('zipCode').patchValue('');
        break;
      case 'city': 
        this.form.get('barangay').patchValue('');
        break;
    }
  }
}
