import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import { ActionModal, InsuranceSubType, InsuranceType } from '../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, RadioButtonComponent, DateFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { NewVehicleInsuranceDetailService } from '../../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { INSURANCE_STATUS } from '../../../../constants/new-vehicle-insurance-detail.const';
import { ImportFilesComponent } from '../../../../../../core/shared/import-files/import-files.component';
import { CONSENT_FORM } from '../../../../constants/insurance.const';
import { InsuranceCoverageList } from '../../../../enum';


@Component({
  selector: 'app-modal-insurer-infomation-edit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    MatDialogModule,
    TranslateModule,
    DropdownFormGroupComponent,
    RadioButtonComponent,
    DateFormGroupComponent,
    FormGroupComponent,
    ImportFilesComponent
  ],
  templateUrl: './modal-insurer-infomation-edit.component.html',
  styleUrls: ['./modal-insurer-infomation-edit.component.scss'],
})
export class ModalInsurerInfomationEditComponent implements OnInit {
  mainForm: FormGroup = new FormGroup({});
  insuranceTypeOption: OptionDropdown[] = [];
  insuranceCompanyOption: OptionDropdown[] = [];
  tempCompanyOption: OptionDropdown[] = [];
  mortgageOption: OptionDropdown[] = [];
  insurancePromoList: { code: boolean; name: string }[] = [
    {
      code: false,
      name: 'No',
    },
    {
      code: true,
      name: 'Yes',
    },
  ];
  countAPIOptions: number = 0;
  isRenewal: boolean = false;
  subscription = new Subscription();
  insuranceId: string = '';
  status: string = '';
  vinPaired: boolean = false;
  insuranceCoverage: string = '';
  insuranceType = InsuranceType;
  CONSENT_FORM = CONSENT_FORM;
  subs: Subscription[] = [];
  InsuranceCoverageList = InsuranceCoverageList;

  dialogRef = inject(MatDialogRef<ModalInsurerInfomationEditComponent>);
  data = inject(MAT_DIALOG_DATA);
  insuranceDetailService = inject(NewVehicleInsuranceDetailService);
  loadingService = inject(LoadingService);
  translate = inject(TranslateService);

  public get type(): FormControl {
    return this.mainForm.get('type') as FormControl;
  }

  public get name(): FormControl {
    return this.mainForm.get('name') as FormControl;
  }

  public get promoAvailment(): FormControl {
    return this.mainForm.get('promoAvailment') as FormControl;
  }

  public get mvFileNumber(): FormControl {
    return this.mainForm.get('mvFileNumber') as FormControl;
  }

  public get startDate(): FormControl {
    return this.mainForm.get('startDate') as FormControl;
  }

  public get mortgage(): FormControl {
    return this.mainForm.get('mortgage') as FormControl;
  }

  public get mortgageAddress(): FormControl {
    return this.mainForm.get('mortgageAddress') as FormControl;
  }

  public get drivingScore(): FormControl {
    return this.mainForm.get('drivingScore') as FormControl;
  }

  public get telematicDiscount(): FormControl {
    return this.mainForm.get('telematicDiscount') as FormControl;
  }

  public get consentForm(): FormControl {
    return this.mainForm.get('consentForm') as FormControl;
  }

  public get consentDate(): FormControl {
    return this.mainForm.get('consentDate') as FormControl;
  }
  ngOnInit() {
    this.mainForm = this.data?.mainForm as FormGroup;
    this.isRenewal = this.data?.isRenewal;
    this.insuranceId = this.data?.insuranceId;
    this.status = this.data?.status;
    this.vinPaired = this.data?.vinPaired;
    this.insuranceCoverage = this.data?.insuranceCoverage;

    if (!this.mainForm.get('mortgage')) {
      this.mainForm.addControl('mortgage', new FormControl(null));
    }
    if (!this.mainForm.get('mortgageAddress')) {
      this.mainForm.addControl('mortgageAddress', new FormControl(null));
    }

    this.countAPIOptions = 3;
    this.loadingService.showLoader();
    this.getMortgagesOption();
    this.getInsuranceTypeOption();

    const mortgageSub = this.mortgage.valueChanges.subscribe(
      (mortgageValue) => {
        this.updateMortgageAddressValidation(mortgageValue);
      }
    );
    this.subs.push(mortgageSub);
    this.updateMortgageAddressValidation(this.mortgage.value);
  }

  ngOnDestroy(): void {
    this.subs.forEach((sub) => sub.unsubscribe());
  }

  onCancel() {
    this.dialogRef.close();
  }

  handleCountAPI() {
    this.countAPIOptions -= 1;
    if (this.countAPIOptions === 0) {
      this.loadingService.hideLoader();
    }
  }

  onSubmit() {
    this.dialogRef.close({
      action: ActionModal.Submit,
    });
  }

  getInsuranceTypeOption() {
    this.subscription.add(
      this.insuranceDetailService
        .getInsuranceTypeOption()
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe((data: any) => {
          this.getCompanyOption();
          if ((this.status === INSURANCE_STATUS.PENDING || this.status === INSURANCE_STATUS.CANCELLED || this.status === INSURANCE_STATUS.FAILED) && this.vinPaired && this.insuranceCoverage !== InsuranceCoverageList.COMP_CTPL) {
            this.insuranceTypeOption = data?.enums?.insuranceType || [];
          } else {
            this.insuranceTypeOption = data?.enums?.insuranceType && data?.enums?.insuranceType.length ? 
            data?.enums?.insuranceType.filter(item => item.code !== InsuranceType.TELEMATICS) : [];
          }

        }, error => {
          this.getCompanyOption();
        })
    );
  }

  get isSubmitDisabled(): boolean {
    if (!this.mainForm) {
      return true;
    }
    const formIsInvalid = this.mainForm.invalid;
    const mortgageHasValue = !!this.mortgage.value;

    if (!formIsInvalid) {
      return false;
    }
    if (formIsInvalid && !mortgageHasValue) {
      let otherFieldsInvalid = false;
      Object.keys(this.mainForm.controls).forEach((key) => {
        const control = this.mainForm.get(key);
        if (key !== 'mortgageAddress' && control?.invalid) {
          otherFieldsInvalid = true;
        }
      });
      return otherFieldsInvalid;
    }
    return true;
  }

  getMortgagesOption() {
    this.subscription.add(
      this.insuranceDetailService
        .getMortgagesOption()
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe((data: any) => {
          this.mortgageOption = [
            {
              name: this.translate.instant(
                'insurance.modal.mortgagePlaceholder'
              ),
              code: null,
            },
            ...(data?.items || []),
          ];
        })
    );
  }

  getCompanyOption() {
    this.subscription.add(
      this.insuranceDetailService
        .getCompanyOption(this.insuranceId)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe((data: any) => {
          this.tempCompanyOption = this.insuranceCompanyOption = data?.items;
          const tempType = this.type.value;
          if (tempType) {
            this.filterCompanyOption(tempType);
          }
          if (this.mainForm.controls['supportIntegration'].value) {
            this.startDate?.addValidators(Validators.required);
            this.startDate?.updateValueAndValidity();

            if (this.isRenewal) {
              this.mvFileNumber?.addValidators(Validators.required);
              this.mvFileNumber?.updateValueAndValidity();
            }
          }
        })
    );
  }

  updateMortgageAddressValidation(mortgageValue: any): void {
    if (mortgageValue) {
      this.mortgageAddress.setValidators(Validators.required);
    } else {
      this.mortgageAddress.clearValidators();
    }
    this.mortgageAddress.updateValueAndValidity({ emitEvent: false });
  }

  insuranceCompanyChange(e: any) {
    const item: any = this.insuranceCompanyOption.find(item => item.code === e.value);
    this.mainForm.controls['companyName'].patchValue(item?.name);
    this.mainForm.controls['supportIntegration'].patchValue(item?.supportIntegration || false);
    if (this.mainForm.controls['supportIntegration'].value) {
      this.mortgage?.addValidators(Validators.required);
      this.mortgageAddress?.addValidators(Validators.required);
      this.startDate?.addValidators(Validators.required);
      if (this.isRenewal) {
        this.mvFileNumber?.addValidators(Validators.required);
      }
    } else {
      this.mortgage?.clearValidators();
      this.mortgageAddress?.clearValidators();
      this.startDate?.clearValidators();
      if (this.isRenewal) {
        this.mvFileNumber?.clearValidators();
      }
    }
    this.mortgage?.updateValueAndValidity();
    this.mortgageAddress?.updateValueAndValidity();
    this.startDate?.updateValueAndValidity();
    if (this.isRenewal) {
      this.mvFileNumber?.updateValueAndValidity();
    }
  }

  mortgageChange(e: any) {
    const item = this.mortgageOption.find(item => item.code === e.value);
    this.mainForm.controls['mortgageName'].patchValue(item?.name)
  }

  insuranceTypeChange(e: any) {
    this.name.patchValue("");
    this.mainForm.controls['companyName'].patchValue("");
    this.filterCompanyOption(e.value);
  }

  filterCompanyOption(type: string) {
    switch(type) {
      case InsuranceType.TOYOTA:
        this.insuranceCompanyOption = this.tempCompanyOption.filter(item => item?.type && item?.type?.code && item?.type?.code === InsuranceSubType.TI);
        this.consentForm?.clearValidators();
        this.consentDate?.clearValidators();
        this.drivingScore?.clearValidators();
        this.telematicDiscount?.clearValidators();
        this.consentDate?.updateValueAndValidity();
        this.consentForm?.updateValueAndValidity();
        this.drivingScore?.updateValueAndValidity();
        this.telematicDiscount?.updateValueAndValidity();
        break;
      case InsuranceType.OTH:
        this.insuranceCompanyOption = this.tempCompanyOption.filter(item => item?.type && item?.type?.code && item?.type?.code === InsuranceSubType.NON_TI);
        this.consentForm?.clearValidators();
        this.consentDate?.clearValidators();
        this.drivingScore?.clearValidators();
        this.telematicDiscount?.clearValidators();
        this.consentDate?.updateValueAndValidity();
        this.consentForm?.updateValueAndValidity();
        this.drivingScore?.updateValueAndValidity();
        this.telematicDiscount?.updateValueAndValidity();
        break;
      case InsuranceType.TELEMATICS:
        this.insuranceCompanyOption = this.tempCompanyOption.filter(item => item?.supportTelematic);
        this.consentForm?.addValidators(Validators.required);
        this.consentDate?.addValidators(Validators.required);
        this.drivingScore?.addValidators(Validators.required);
        this.telematicDiscount?.addValidators(Validators.required);
        this.consentDate?.updateValueAndValidity();
        this.consentForm?.updateValueAndValidity();
        this.drivingScore?.updateValueAndValidity();
        this.telematicDiscount?.updateValueAndValidity();
        break; 
    }
  }

  handleFiles(event) {
    this.mainForm.controls['consentFormReal']?.patchValue(event);
  }
}
