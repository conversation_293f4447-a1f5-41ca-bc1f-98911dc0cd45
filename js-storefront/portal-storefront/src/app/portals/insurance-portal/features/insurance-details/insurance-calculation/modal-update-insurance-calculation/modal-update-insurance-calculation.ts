import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  inject,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  debounceTime,
  finalize,
  Subject,
} from 'rxjs';
import { toNumber, sum, sumBy } from 'lodash-es';
import { IconModule } from '../../../../../../core/icon/icon.module';
import {
  LoadingService,
  NotificationService,
} from '../../../../../../core/services';
import { FormGroupComponent } from '../../../../../../core/shared';
import {
  InsuranceQuotation,
  UpdateInsuranceCostRequest,
} from '../../../../interfaces';
import { InsuranceCalculationService } from '../../../../services/insurance-calculation/insurance-calculation.service';
import { MatDividerModule } from '@angular/material/divider';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { InsuranceInput } from '../../../../enum';
@Component({
  selector: 'app-modal-update-insurance-calculation',
  templateUrl: './modal-update-insurance-calculation.html',
  styleUrls: ['./modal-update-insurance-calculation.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    FormGroupComponent,
    MatCheckboxModule,
    MatDialogModule,
    FormsModule,
    MatDividerModule,
    LoadingComponent
  ],
  providers: [LoadingService, NotificationService, InsuranceCalculationService],
  standalone: true,
})
export class ModalUpdateInsuranceCalculationComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter();

  data = inject(MAT_DIALOG_DATA);

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  private insuranceService = inject(InsuranceCalculationService);

  private fb = inject(FormBuilder);
  private cdr = inject(ChangeDetectorRef);
  private calculateSubject = new Subject<string>();

  dialogRef = inject(MatDialogRef<ModalUpdateInsuranceCalculationComponent>);
  installmentPayment: boolean = false;
  netPremiumChanged: boolean = false;
  netCoverageChanged: boolean = false;
  totalPremiumChanged: boolean = false;
  curNetPremiumBeforeTaxPremium: number = 0;
  curTotal: number = 0;
  docsRatePercent: number = 0;
  form: FormGroup;
  typeInput: string = '';
  controlNameRate: string = '';
  nameInput: string = '';
  INSURANCE_INPUT = InsuranceInput;
  vatRatePercent: number = 0
  insuranceItems = [
    {
      label: this.translateService.instant('insuranceCalculation.odTheft'),
      controlName: 'odTheft',
      rate: { value: 0, disable: false },
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.aon'),
      controlName: 'aon',
      rate: { value: 0, disable: false },
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.rscc'),
      controlName: 'rscc',
      rate: { value: 0, disable: false },
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.ctpl'), 
      controlName: 'ctpl',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.propertyDamage'
      ),
      controlName: 'propertyDamage',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.personalAccident'
      ),
      controlName: 'personalAccident',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.autoPassenger'
      ),
      controlName: 'autoPassenger',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.excessiveBodyInjury'
      ),
      controlName: 'excessiveBodyInjury',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.lossOfUse'),
      controlName: 'lossOfUse',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.roadsideAssistance'
      ),
      controlName: 'roadsideAssistance',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.vehicleAccessories'
      ),
      controlName: 'vehicleAccessories',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.netPremiumBeforeTax'
      ),
      controlName: 'netPremiumBeforeTax',
      premium: { value: 0, disable: true },
      coverage: { value: 0, disable: true },
      bold: true,
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.documentaryStamp'
      ),
      controlName: 'documentaryStamp',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.vat'),
      controlName: 'vat',
      premium: { value: 0, disable: true },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.localGovernmentTax'
      ),
      controlName: 'localGovernmentTax',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.grossPremium'),
      controlName: 'grossPremium',
      premium: { value: 0, disable: true },
      bold: true,
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.authenticationFee'
      ),
      controlName: 'authenticationFee',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.totalPremium'),
      controlName: 'total',
      premium: { value: 0, disable: true },
      bold: true,
    },
  ];

  excludedPremiumFields = [
    'documentaryStamp',
    'vat',
    'localGovernmentTax',
    'grossPremium',
    'authenticationFee',
    'total',
    'netPremiumBeforeTax',
  ];

  excludedPremiumFieldsInput = [
    'documentaryStampPremium',
    'vatPremium',
    'localGovernmentTaxPremium',
    'grossPremiumPremium',
    'authenticationFeePremium',
    'totalPremium',
    'netPremiumBeforeTaxPremium',
  ];

  onlyPremiumFormHaveRateFields = ['rscc', 'odTheft', 'aon', 'vehicleAccessories'];
  onlyCoverageFormNotHaveRateFields = ['ctplCoverage', 'propertyDamageCoverage', 'personalAccidentCoverage', 'excessiveBodyInjuryCoverage', 'lossOfUseCoverage', 'roadsideAssistanceCoverage'];
  
  ngOnInit(): void {
    this.createInsuranceForm();
    this.prefillFormWithData(this.data.insuranceCalculation);
    this.calculateSubject.pipe(debounceTime(800)).subscribe((changedControlName) => {
      if (this.typeInput === 'Premium' || this.typeInput === 'Rate') this.netPremiumChanged = true;
      if (this.typeInput === 'Coverage') this.netCoverageChanged = true;
      this.calculatePremiums(changedControlName);
    });
  }
    
  get installmentPaymentForm() {
    return this.form.get('installmentPayment') as FormControl;
  }

  get termsForm() {
    return this.form.get('terms') as FormControl;
  }

  get monthlyPaymentForm() {
    return this.form.get('monthlyPayment') as FormControl;
  }

  private createInsuranceForm(): void {
    this.form = this.fb.group({
      installmentPayment: new FormControl(false),
      noVAT: new FormControl(false),
      terms: new FormControl({ value: '', disabled: true }, [
        Validators.required,
        Validators.min(1),
      ]),
      monthlyPayment: new FormControl({ value: 0, disabled: true }),
    });

    this.form.addControl(
      'netPremiumBeforeTaxPremium',
      new FormControl({ value: 0, disabled: true })
    );
    this.form.addControl(
      'netPremiumBeforeTaxCoverage',
      new FormControl({ value: 0, disabled: true })
    );

    this.insuranceItems.forEach((item) => {
      if (item.rate) {
        const rateControl = new FormControl(item.rate.value ?? 0, [
          Validators.required,
          Validators.pattern(/^\d+(\.\d{1,2})?$/),
        ]);
        if (item.rate.disable) {
          rateControl.disable();
        }
        this.form.addControl(item.controlName, rateControl);
      }

      if (item.coverage) {
        const coverageControl = new FormControl(item.coverage.value ?? 0, [
          Validators.required,
          Validators.pattern(/^\d+(\.\d{1,2})?$/),
        ]);
        if (item.coverage.disable) {
          coverageControl.disable();
        }
        this.form.addControl(`${item.controlName}Coverage`, coverageControl);
      }

      if (item.premium) {
        const premiumControl = new FormControl(item.premium.value ?? 0);
        const premiumAfterNetControl = new FormControl<number | null>(
          item.premium.value ?? 0,
          [Validators.pattern(/^\d+(\.\d{1,2})?$/)]
        );

        if (item.premium.disable) {
          premiumControl.disable();
        }
        if (!this.excludedPremiumFields.includes(`${item.controlName}`)) {
          this.form.addControl(
            `${item.controlName}Premium`,
            premiumAfterNetControl
          );
        }
        this.form.addControl(`${item.controlName}Premium`, premiumControl);
      }
    });

    this.form.get('installmentPayment')?.valueChanges.subscribe((checked) => {
      if (checked) {
        this.form.get('terms')?.enable();
        this.calculateMonthlyPayment();
      } else {
        this.form.patchValue({ terms: '', monthlyPayment: 0 });
        this.form.get('terms')?.disable();
        this.form.get('monthlyPayment')?.disable();
      }
    });

    // Subscribe to terms changes to calculate monthly payment
    this.form.get('terms')?.valueChanges.subscribe((terms) => {
      this.calculateMonthlyPayment();
    });

    // Subscribe to totalPremium changes to update monthly payment
    this.form.get('totalPremium')?.valueChanges.subscribe(() => {
      this.calculateMonthlyPayment();
    });

    this.form.get('noVAT')?.valueChanges.subscribe((checked) => {
      this.calculatePremiums();
    });
  }

  private calculateMonthlyPayment(): void {
    const totalPremium = this.form.get('totalPremium')?.value ?? 0;
    const terms = this.form.get('terms')?.value ?? 0;

    if (terms > 0) {
      const monthlyPayment = totalPremium / terms;
      this.form.patchValue({
        monthlyPayment: parseFloat(monthlyPayment.toFixed(2)),
      });
    } else {
      this.form.patchValue({ monthlyPayment: 0 });
    }
  }

  private prefillFormWithData(data: InsuranceQuotation): void {
    if (!data) return;
    const premiums: number[] = [];
    const coverages: number[] = [];

    let netPremiumBeforeTaxPremium = 0;
    let netPremiumBeforeTaxCoverage = 0;

    // Populate form controls with values from `insuranceCalculation`
    this.form.patchValue({
      installmentPayment: data.installmentPayment,
      terms: data.installmentMonths,
      monthlyPayment: data.installmentEachMonth,
    });

    this.insuranceItems.forEach((item) => {
      const controlName = item.controlName;

      switch (controlName) {
        case 'odTheft':
          this.form.get(controlName)?.setValue(data.premiumOdIssuingRate ?? 0);
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageOdIssuing ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumOdIssuing ?? 0);
          break;

        case 'aon':
          this.form.get(controlName)?.setValue(data.premiumAonIssuingRate ?? 0);
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageAonIssuing ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumAonIssuing ?? 0);
          break;

        case 'rscc':
          this.form
            .get(controlName)
            ?.setValue(data.premiumRiotStrikeCivilCommotionRate ?? 0);
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageRiotStrikeCivilCommotion ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumRiotStrikeCivilCommotion ?? 0);
          break;

        case 'ctpl':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageThirdPartyLiability ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumThirdPartyLiability ?? 0);
          break;

        case 'propertyDamage':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coveragePropertyDamage ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumPropertyDamage ?? 0);
          break;

        case 'personalAccident':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coveragePersonalAccident ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumPersonalAccident ?? 0);
          break;

        case 'excessiveBodyInjury':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageExcessBodilyInjury ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumExcessBodilyInjury ?? 0);
          break;

        case 'lossOfUse':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageLossOfUse ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumLossOfUse ?? 0);
          break;

        case 'roadsideAssistance':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageRoadsideAssitance ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumRoadsideAssitance ?? 0);
          break;

        case 'vehicleAccessories':
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data?.coverageVehicleAccessories ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data?.premiumVehicleAccessories ?? 0);
          break;

        case 'documentaryStamp':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.docsAmount ?? 0);
            this.docsRatePercent = data.docsRate
          break;

        case 'vat':
          this.form.get(`${controlName}Premium`)?.setValue(data.vatAmount ?? 0);
          this.vatRatePercent = data.vatRate;
          if (data.vatAmount === 0) {
            this.form.get('noVAT')?.setValue(true);
          }
          break;

        case 'localGovernmentTax':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.localGovernmentStamp ?? 0);
          break;

        case 'grossPremium':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.grossPremium?.value ?? 0);
          break;

        case 'authenticationFee':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.authenticationCost ?? 0);
          break;

        case 'total':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.totalPremium?.value ?? 0);
          break;
      }

      if (!this.excludedPremiumFields.includes(`${controlName}`)) {
        const premium =
          toNumber(this.form.get(`${controlName}Premium`)?.value) || 0;
        const coverage =
          toNumber(this.form.get(`${controlName}Coverage`)?.value) || 0;

        if (premium > 0) {
          premiums.push(premium);
        }
        if (coverage > 0) {
          coverages.push(coverage);
        }
      }
    });

    netPremiumBeforeTaxPremium = sum(premiums);
    netPremiumBeforeTaxCoverage = sum(coverages);
    this.curNetPremiumBeforeTaxPremium = +netPremiumBeforeTaxPremium.toFixed(2);
    this.curTotal = data.totalPremium?.value ?? 0
    this.form.patchValue({
      netPremiumBeforeTaxCoverage: this.handleNetPremium(
        netPremiumBeforeTaxCoverage
      ),
      netPremiumBeforeTaxPremium: this.handleNetPremium(
        netPremiumBeforeTaxPremium
      ),
      documentaryStampPremium: data.docsAmount ?? 0,
      vatPremium: data.vatAmount ?? 0,
      localGovernmentTaxPremium: data.localGovernmentStamp ?? 0,
      grossPremiumPremium: data.grossPremium?.value ?? 0,
      totalPremium: data.totalPremium?.value ?? 0,
    });
    this.cdr.detectChanges();
  }

  private handleNetPremium(num: number): number {
     if (Number.isInteger(num)) {
       return parseFloat(num.toFixed(1));
     } else {
       return parseFloat(num.toFixed(2));
     }
  }
    
  calculatePremiums(changedControlName?: string): void {
    let newNetPremium = 0;
    let newNetCoverage = 0;
    const premiums: number[] = [];
    const coverages: number[] = [];
  
    let baseControlName = changedControlName;
    let changeType = '';
    if (changedControlName?.endsWith('Premium')) {
      baseControlName = changedControlName.replace('Premium', '');
      changeType = 'Premium';
    } else if (changedControlName?.endsWith('Coverage')) {
      baseControlName = changedControlName.replace('Coverage', '');
      changeType = 'Coverage';
    } else {
      changeType = 'Rate';
    }
  
    if (this.onlyPremiumFormHaveRateFields.includes(baseControlName)) {
      // Special case for vehicleAccessories: use odTheft's rate
      let rate = 0;
      if (baseControlName === 'vehicleAccessories') {
        rate = toNumber(this.form.get('odTheft')?.value) || 0;
      } else {
        rate = toNumber(this.form.get(baseControlName)?.value) || 0;
      }
      const coverage = toNumber(this.form.get(`${baseControlName}Coverage`)?.value) || 0;
      const premium = toNumber(this.form.get(`${baseControlName}Premium`)?.value) || 0;
  
      if (changeType === 'Rate' || changeType === 'Coverage') {
        // Rate or Coverage changed: recalculate Premium only
        const newPremium = rate > 0 ? (coverage * rate) / 100 : 0;
        this.form.get(`${baseControlName}Premium`)?.setValue(newPremium.toFixed(2), { emitEvent: false });
      } else if (changeType === 'Premium') {
        // Premium changed: recalculate Coverage only
        const newCoverage = rate > 0 ? (premium * 100) / rate : 0;
        this.form.get(`${baseControlName}Coverage`)?.setValue(newCoverage.toFixed(2), { emitEvent: false });
      }

      if (baseControlName === 'odTheft') {
        const vehicleAccCoverage = toNumber(this.form.get(`vehicleAccessoriesCoverage`)?.value) || 0;
        const newPremium = rate > 0 ? (vehicleAccCoverage * rate) / 100 : 0;
        this.form.get(`vehicleAccessoriesPremium`)?.setValue(newPremium.toFixed(2), { emitEvent: false });
      }
    }
  
    this.insuranceItems.forEach((item) => {
      const controlName = item.controlName;
      if (!this.excludedPremiumFields.includes(controlName)) {
        const premium = toNumber(this.form.get(`${controlName}Premium`)?.value) || 0;
        const coverage = toNumber(this.form.get(`${controlName}Coverage`)?.value) || 0;
        if (premium > 0) premiums.push(premium);
        if (coverage > 0) coverages.push(coverage);
      }
    });
  
    newNetPremium = sumBy(premiums);
    newNetCoverage = sumBy(coverages);
  
    if (this.curNetPremiumBeforeTaxPremium !== newNetPremium) {
      this.netPremiumChanged = true;
      this.curNetPremiumBeforeTaxPremium = newNetPremium;
    }
    if (this.form.get('netPremiumBeforeTaxCoverage')?.value !== newNetCoverage) {
      this.netCoverageChanged = true;
    }
  
    let documentaryStamp;
    let localGovernmentTax;
    let vat;
    let authenticationFeePremium;
    // Calculate derived fields
    if (this.excludedPremiumFields.includes(this.nameInput) || this.onlyCoverageFormNotHaveRateFields.includes(this.controlNameRate)) {
      documentaryStamp = toNumber(this.form.get(`documentaryStampPremium`)?.value) || 0;
      localGovernmentTax = toNumber(this.form.get(`localGovernmentTaxPremium`)?.value) || 0;
      authenticationFeePremium = toNumber(this.form.get(`authenticationFeePremium`)?.value) || 0;
    } else {
      documentaryStamp = newNetPremium * (this.docsRatePercent / 100);
      localGovernmentTax = toNumber(this.form.get(`localGovernmentTaxPremium`)?.value) || 0;
      authenticationFeePremium = toNumber(this.form.get(`authenticationFeePremium`)?.value) || 0;
    }

    vat = this.form.get('noVAT')?.value 
    ? 0
    : newNetPremium * this.vatRatePercent;

    const grossPremium =
      newNetPremium + documentaryStamp + localGovernmentTax + vat;
    const total = parseFloat((grossPremium + authenticationFeePremium).toFixed(2));
    // Update form values without emitting events
    this.form.patchValue(
      {
        netPremiumBeforeTaxPremium: this.handleNetPremium(newNetPremium),
        netPremiumBeforeTaxCoverage: this.handleNetPremium(newNetCoverage),
        documentaryStampPremium: parseFloat(documentaryStamp.toFixed(2)),
        localGovernmentTaxPremium: parseFloat(localGovernmentTax.toFixed(2)),
        vatPremium: parseFloat(vat.toFixed(2)),
        grossPremiumPremium: parseFloat(grossPremium.toFixed(2)),
        totalPremium: parseFloat(total.toFixed(2)),
      },
      { emitEvent: false }
    );

    if (this.data?.insuranceCalculation?.vatAmount != this.form.get(`vatPremium`)?.value) {
      this.form.get(`vatPremium`)?.markAsDirty();
    } else {
      this.form.get(`vatPremium`)?.markAsDirty();
    }

    this.totalPremiumChanged =
      this.form.get(`documentaryStampPremium`).dirty ||
      this.form.get(`localGovernmentTaxPremium`).dirty ||
      this.form.get(`vatPremium`).dirty ||
      this.form.get(`authenticationFeePremium`).dirty ||
      this.netPremiumChanged;
  
    if (this.form.get('installmentPayment')?.value) {
      const monthlyPayment = (total ?? 0)/(this.termsForm?.value ?? 0);
      this.monthlyPaymentForm.patchValue(monthlyPayment.toFixed(2))
    }
    this.cdr.detectChanges();
  }

  updateInsuranceCalculation(): void {
    if (this.form.valid) {
      try {
      const updatedData = this.form;
      const insuranceCode = this.data.insuranceId;
      const vehiclePaidPrice = this.data.paidPriceVehicle;

      const netPremium =
      parseFloat(this.form.get('netPremiumBeforeTaxPremium')?.value) || 0;
      const netCoverage =
      parseFloat(this.form.get('netPremiumBeforeTaxCoverage')?.value) || 0;
      
      const formTotalPremium =
      parseFloat(this.form.get('totalPremium')?.value) || 0;
      
      const dataSend = this.responDataInsuranceCalculation(
        vehiclePaidPrice,
        updatedData,
        formTotalPremium,
        netPremium,
        netCoverage
      );
      // If there is create insurance, update the calculation only, no need to save data yet
      if (this.data.insuranceInput === this.INSURANCE_INPUT.CREATE) {
        this.insuranceUpdated.emit(dataSend);
        this.dialogRef.close();
      } else { // If there is new/renewal insurance, save the data
        if (!insuranceCode) {
          this.notificationService.showError(
            this.translateService.instant(
              'newVehicleInsurance.detail.insuranceCalculation.codeMissing'
            )
          );
          return;
        }
        this.loadingService.showLoader();
        this.insuranceService
          .updateInsuranceCost(insuranceCode, dataSend, this.data?.insuranceInput === this.INSURANCE_INPUT.RENEWAL)
          .pipe(
            finalize(() => {
              this.loadingService.hideLoader();
            })
          )
          .subscribe(res => {
            if (res.code === '200') {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'newVehicleInsurance.detail.insuranceCalculation.updateSuccess'
                )
              );
              this.insuranceUpdated.emit();
              this.dialogRef.close(updatedData.value);
            }
          }, err => {
            this.notificationService.showError(
              this.translateService.instant(
                'newVehicleInsurance.detail.insuranceCalculation.updateFailed'
              )
            );
          });
        }
      } catch(err) {
        console.log(err)
        this.loadingService.hideLoader();
      }
    }
  }

  responDataInsuranceCalculation(
    vehiclePaidPrice: number | string,
    data: any,
    total: number,
    netPremium: number,
    netCoverage: number
  ): UpdateInsuranceCostRequest {
    const toNumber = (value: any): number => parseFloat(value ?? 0.0);
    const toInt = (value: any): number => parseInt(value ?? 0, 10);
    return {
      vehiclePaidPrice: toNumber(vehiclePaidPrice),
      premiumOdIssuingRate: toNumber(this.form.controls['odTheft']?.value),
      premiumOdIssuing: toNumber(this.form.controls['odTheftPremium']?.value),
      premiumAonIssuingRate: toNumber(this.form.controls['aon']?.value),
      premiumAonIssuing: toNumber(this.form.controls['aonPremium']?.value),
      premiumRiotStrikeCivilCommotionRate: toNumber(this.form.controls['rscc']?.value),
      premiumRiotStrikeCivilCommotion: toNumber(this.form.controls['rsccPremium']?.value),
      premiumThirdPartyLiability: toNumber(this.form.controls['ctplPremium']?.value),
      premiumExcessBodilyInjury: toNumber(this.form.controls['excessiveBodyInjuryPremium']?.value),
      premiumPropertyDamage: toNumber(this.form.controls['propertyDamagePremium']?.value),
      premiumPersonalAccident: toNumber(this.form.controls['personalAccidentPremium']?.value),
      premiumAutoPersonalAccident: toNumber(this.form.controls['autoPassengerPremium']?.value),
      premiumLossOfUse: toNumber(this.form.controls['lossOfUsePremium']?.value),
      premiumRoadsideAssitance: toNumber(this.form.controls['roadsideAssistancePremium']?.value),
      premiumVehicleAccessories: toNumber(this.form.controls['vehicleAccessoriesPremium']?.value),
      localGovernmentStampRate: toNumber(this.form.controls['localGovernmentStampRate']?.value),
      netPremium: netPremium,
      docsAmount: toNumber(this.form.controls['documentaryStampPremium']?.value),
      vatAmount: toNumber(this.form.controls['vatPremium']?.value),
      localGovernmentStamp: toNumber(this.form.controls['localGovernmentTaxPremium']?.value),
      totalPremium: total,
      coverageOdIssuing: toNumber(this.form.controls['odTheftCoverage']?.value),
      coverageAonIssuing: toNumber(this.form.controls['aonCoverage']?.value),
      coverageThirdPartyLiability: toNumber(this.form.controls['ctplCoverage']?.value),
      coverageExcessBodilyInjury: toNumber(this.form.controls['excessiveBodyInjuryCoverage']?.value),
      coveragePropertyDamage: toNumber(this.form.controls['propertyDamageCoverage']?.value),
      coveragePersonalAccident: toNumber(this.form.controls['personalAccidentCoverage']?.value),
      coverageAutoPersonalAccident: toNumber(this.form.controls['autoPassengerCoverage']?.value),
      coverageRiotStrikeCivilCommotion: toNumber(this.form.controls['rsccCoverage']?.value),
      coverageLossOfUse: toNumber(this.form.controls['lossOfUseCoverage']?.value),
      coverageRoadsideAssitance: toNumber(this.form.controls['roadsideAssistanceCoverage']?.value),
      coverageVehicleAccessories: toNumber(this.form.controls['vehicleAccessoriesCoverage']?.value),
      totalCoverage: netCoverage,
      authenticationCost: toNumber(this.form.controls['authenticationFeePremium']?.value),
      installmentPayment: !!this.form.controls['installmentPayment']?.value,
      installmentMonths: toInt(this.form.controls['terms']?.value),
      installmentEachMonth: this.monthlyPaymentForm.value,
    };
  }

  updateOnInput(event: Event, controlName: string, type, name: string): void {
    const input = event.target as HTMLInputElement;
    const newValue = parseFloat(input.value);
    if (!this.excludedPremiumFieldsInput.includes(controlName)) {
      this.typeInput = type;
    }
    if (this.onlyPremiumFormHaveRateFields.includes(name)) {
      this.netPremiumChanged = true;
    }
    this.controlNameRate = controlName;
    this.nameInput = name;
    if (!isNaN(newValue)) {
      this.calculateSubject.next(controlName);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
