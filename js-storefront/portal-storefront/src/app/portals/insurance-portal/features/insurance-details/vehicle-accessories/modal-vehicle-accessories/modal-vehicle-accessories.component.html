
<h2 class="title-dialog">
    @if (data.status === 'add') {
        {{ "vehicleAccessories.modal.addItem" | translate }}
    } @else if (data.status === 'edit') {
        {{ "vehicleAccessories.modal.editItem" | translate }}
    }
</h2>

<mat-dialog-content
  class="container-dialog"
  [class.loading]="loadingService.isLoading"
>
  <form [formGroup]="form">
    <div class="row">
      <div class="col-6">
        <app-form-group
          class="modal-vehicle-accesspries__input"
          [label]="'vehicleAccessories.item' | translate"
          [required]="true"
          [isReadOnly]="isReadonly"
          [control]="itemControl"
          [placeholder]="'validation.plsEnter' | translate"
        ></app-form-group>
      </div>
      <div class="col-6">
        <app-form-group
          class="modal-vehicle-accesspries__input"
          [label]="'vehicleAccessories.srp' | translate"
          [isReadOnly]="isReadonly"
          [required]="true"
          [control]="srpControl"
          [useCurrencyFormat]="true"
          [placeholder]="'' | translate"
          (onKeypress)="validateInputDecimal($event)"
        ></app-form-group>
        
      </div>
    </div>
    <div class="row">
        @if (data.status === 'edit') {
            <div class="col-6">
                <app-form-group class="modal-vehicle-accesspries__input" [label]="'vehicleAccessories.quantity' | translate" [isReadOnly]="isReadonly"  [required]="true"
                    [control]="quantityControl" (onKeypress)="validateInput($event)" (paste)="onPaste($event)" ></app-form-group>
            </div>
            <div class="col-6">
                <app-form-group class="modal-vehicle-accesspries__input" [label]="'vehicleAccessories.coverage' | translate"  [useCurrencyFormat]="true" [required]="true"
                    [control]="coverageAmountControl" (onKeypress)="validateInputDecimal($event)"></app-form-group>
            </div>
        } @else {
            <div class="col-12">
                <app-form-group class="modal-vehicle-accesspries__input" [label]="'vehicleAccessories.quantity' | translate" [required]="true"
                    [control]="quantityControl" (onKeypress)="validateInput($event)" (paste)="onPaste($event)" ></app-form-group>
            </div>
        }
    </div>
    @if (data.status === 'edit') {
        <div class="row">
            <div class="col-12">
                <mat-checkbox class="modal-vehicle-accesspries__input" class="custom-mat-checkbox" formControlName="approved">
                    {{ "vehicleAccessories.approved" | translate }}
                </mat-checkbox>
            </div>
        </div>
    }
  </form>
</mat-dialog-content>
<mat-dialog-actions class="action-dialog modal-add-va-action">
  <button class="btn-quaternary" (click)="onCancel()">
    {{data.cancelBtn}}
  </button>
  <button
    class="btn-primary"
    [disabled]="form.invalid"
    (click)="onSubmit()"
  >
    {{data.submitBtn}}
  </button>
</mat-dialog-actions>

