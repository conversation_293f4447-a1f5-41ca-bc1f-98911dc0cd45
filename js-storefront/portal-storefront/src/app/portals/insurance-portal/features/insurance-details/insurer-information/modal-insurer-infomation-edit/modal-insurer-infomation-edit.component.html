<div class="insurance-info-form">
  <h2 class="title-dialog">
    {{ "insurance.modal.updateInsurerInformation" | translate }}
  </h2>
  <mat-dialog-content class="container-dialog">
    <form [formGroup]="mainForm">
      <app-dropdown-form-group
        [label]="'insurance.modal.insuranceType' | translate"
        [control]="type"
        [required]="true"
        [options]="insuranceTypeOption"
        [placeholder]="'insurance.modal.insuranceTypePlaceholder' | translate"
        (changeOption)="insuranceTypeChange($event)"
      ></app-dropdown-form-group>
      <app-dropdown-form-group
        [label]="'insurance.modal.insuranceCompany' | translate"
        [control]="name"
        [required]="true"
        [options]="insuranceCompanyOption"
        [placeholder]="'insurance.modal.insuranceCompanyPlaceholder' | translate"
        (changeOption)="insuranceCompanyChange($event)"
      ></app-dropdown-form-group>
        <app-date-form-group
          [label]="'manageLoan.startDate' | translate"
          [control]="startDate"
          controlId="startDate"
          [required]="true"
          [placeholder]="'insurance.modal.startDatePlaceholder' | translate"
          [errorMessage]="
            'common.requiredField'
              | translate : { field: ('manageLoan.startDate' | translate) }
          "
        ></app-date-form-group>

        @if (isRenewal && this.mainForm.controls['supportIntegration'].value) {
          <app-form-group
            [label]="'insurance.modal.mvFileNumber' | translate"
            [control]="mvFileNumber"
            [required]="true"
            [placeholder]="'insurance.modal.mvFileNumberPlaceholder' | translate"
          ></app-form-group>
        }
        <app-dropdown-form-group
          [label]="'insurance.modal.mortgage' | translate"
          [control]="mortgage"
          [required]="mainForm.controls['supportIntegration']?.value"
          [options]="mortgageOption"
          [placeholder]="'insurance.modal.mortgagePlaceholder' | translate"
          (changeOption)="mortgageChange($event)"
        ></app-dropdown-form-group>

        <app-form-group
          [label]=" 'insurance.modal.mortgageAddress' | translate "
          [control]="mortgageAddress"
          [required]="!!mortgage.value"
          [placeholder]=" 'insurance.modal.mortgageAddressPlaceholder' | translate "
          [errorMessage]="
          'validation.fillInData' | translate
        "
        ></app-form-group>
      @if (type?.value === insuranceType.TOYOTA && vinPaired && isRenewal && insuranceCoverage !== InsuranceCoverageList.COMP_CTPL) {
        <app-form-group
          [label]=" 'insurance.modal.drivingScore' | translate "
          [control]="drivingScore"
          [required]="true"
          [type]="'number'"
          [onlyNumber]="true"
          [placeholder]=" 'insurance.modal.drivingScorePlaceholder' | translate "
        ></app-form-group>
        <app-form-group
          [label]=" 'insurance.modal.telematicsDiscount' | translate "
          [control]="telematicDiscount"
          [required]="true"
          [type]="'number'"
          [onlyNumber]="true"
          [placeholder]=" 'insurance.modal.telematicsDiscountPlaceholder' | translate "
        ></app-form-group>
      }
      @if (type?.value === insuranceType.TELEMATICS && insuranceCoverage !== InsuranceCoverageList.COMP_CTPL) {
        <app-date-form-group
          [label]="'insurance.modal.consentDate' | translate"
          [control]="consentDate"
          controlId="consentDate"
          [placeholder]="'insurance.modal.consentDatePlaceholder' | translate"
          [required]="true"
        ></app-date-form-group>
        @if (isRenewal) {
          <app-form-group
            [label]=" 'insurance.modal.drivingScore' | translate "
            [control]="drivingScore"
            [required]="true"
            [type]="'number'"
            [onlyNumber]="true"
            [placeholder]=" 'insurance.modal.drivingScorePlaceholder' | translate "
          ></app-form-group>
          <app-form-group
            [label]=" 'insurance.modal.telematicsDiscount' | translate "
            [control]="telematicDiscount"
            [required]="true"
            [type]="'number'"
            [onlyNumber]="true"
            [placeholder]=" 'insurance.modal.telematicsDiscountPlaceholder' | translate "
          ></app-form-group>
        }
        <app-import-files
          [label]="'insurance.modal.insuranceConsentForm' | translate"
          [placeholder]="'deviceManagement.noFileSelected' | translate"
          [control]="consentForm"
          [isCheckLimitFile]="true"
          [required]="true"
          [accept]="CONSENT_FORM"
          (filesSelected)="handleFiles($event)"
        ></app-import-files>
      }
      @if(!isRenewal) {
        <app-radio-button
          [label]="'newVehicleInsurance.detail.insurerInformation.insurancePromoAvailment'"
          [control]="promoAvailment"
          [isHorizontal]="true"
          [gap]="100"
          [option]="insurancePromoList"
        ></app-radio-button>
      }
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="action-dialog insurance-info-form__actions">
    <button
      type="button"
      class="btn-quaternary"
      (click)="onCancel()"
    >
      {{ "common.cancel" | translate }}
    </button>
    <button
      class="btn-primary"
      type="submit"
      (click)="onSubmit()"
      [disabled]="isSubmitDisabled"
    >
      {{ "common.update" | translate }}
    </button>
  </mat-dialog-actions>
</div>
