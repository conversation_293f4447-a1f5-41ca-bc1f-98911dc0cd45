@if (loadingService.isLoading) {
  <app-loading></app-loading>
}
  <h2 class="title-dialog update-insurance-calculation-title">
    {{data?.title}}
  </h2>

  <mat-dialog-content
    class="update-insurance-calculation-content"
  >
    <form [formGroup]="form">
      <div class="detail-content">
        <table class="insurance-calculation">
          <tr>
            <th width="25%"></th>
            <th width="25%">
              {{ 'newVehicleInsurance.detail.insuranceCalculation.rate' |
              translate }}
            </th>
            <th width="25%">
              {{ 'newVehicleInsurance.detail.insuranceCalculation.coverage' |
              translate }}
            </th>
            <th width="25%">
              {{ 'newVehicleInsurance.detail.insuranceCalculation.premium' |
              translate }}
            </th>
          </tr>
          <tr *ngFor="let item of insuranceItems">
            @if (!item?.mode) {
            <td class="align-right" [ngClass]="{'bold-label': item.bold}">
              {{ item.label }}
            </td>

            <td class="insurance-calculation__input">
            </td>

            <td class="insurance-calculation__input" 
            [class.padding-bottom-5]="(netPremiumChanged && item.controlName === 'netPremiumBeforeTax') ||
            (netCoverageChanged && item?.controlName === 'netPremiumBeforeTax')">
              <ng-container *ngIf="item.coverage; else emptyCell">
                <input
                  matInput
                  type="number"
                  [readonly]="item.coverage?.disable"
                  [value]="item.coverage?.value ?? ''"
                  formControlName="{{ item.controlName }}Coverage"
                  (input)="updateOnInput($event, item.controlName + 'Coverage', 'Coverage', item.controlName)"
                />
                <div *ngIf="netCoverageChanged && item?.controlName === 'netPremiumBeforeTax'" class="errors-message">
                  <span>{{ 'newVehicleInsurance.detail.insuranceCalculation.netCoverageChange' | translate }}</span>
                </div>
              </ng-container>
            </td>

            <td class="insurance-calculation__input" 
            [class.padding-bottom-5]="(netPremiumChanged && item.controlName === 'netPremiumBeforeTax') ||
            (netCoverageChanged && item?.controlName === 'netPremiumBeforeTax')">
                <ng-container
                  *ngIf="item.premium && item.controlName !== 'netPremiumBeforeTax' && item.controlName !== 'total'; else specialFields"
                >
                  <input
                    matInput
                    type="number"
                    [readonly]="item.premium?.disable"
                    formControlName="{{ item.controlName }}Premium"
                    (input)="updateOnInput($event, item.controlName + 'Premium', 'Premium', item.controlName)"
                  />
                </ng-container>
                <ng-template #specialFields>
                    <ng-container
                      *ngIf="item.controlName === 'netPremiumBeforeTax'"
                    >
                      <input
                        matInput
                        type="number"
                        formControlName="netPremiumBeforeTaxPremium"
                        [readonly]="item.premium?.disable"
                      />
                      <div *ngIf="netPremiumChanged" class="errors-message">
                        <span>{{ 'newVehicleInsurance.detail.insuranceCalculation.netPremiumChange' | translate }}</span>
                      </div>
                    </ng-container>
    
                    <ng-container *ngIf="item.controlName === 'total'">
                      <input
                        matInput
                        type="number"
                        formControlName="totalPremium"
                        [readonly]="item.premium?.disable"
                      />
                      <div *ngIf="totalPremiumChanged" class="errors-message">
                        <span
                          >{{ 'newVehicleInsurance.detail.insuranceCalculation.totalPremiumChange' |
                        translate }}</span
                        >
                      </div>
                    </ng-container>
                </ng-template>
              </td>
            } @else {
              @if (item?.mode === 'noted' && !isRenewal) {
                <td class="non-padding"></td>
                <td class="non-padding"></td>
                <td class="non-padding"></td>
                <td class="non-padding">
                  <div class="noted-message">
                    <span>{{ 'newVehicleInsurance.detail.insuranceCalculation.premiumMustBe3Years' | translate }}</span>
                  </div>
                </td>
              }
            }


            <ng-template #emptyCell>
              <td></td>
            </ng-template>
          </tr>
        </table>
      </div>
    </form>
    <mat-divider></mat-divider>
    <div [formGroup]="form" class="checkbox-wrapper">
        <mat-checkbox class="custom-mat-checkbox" formControlName="noVAT">
            {{ "newVehicleInsurance.detail.insuranceCalculation.noVat" | translate }}
        </mat-checkbox>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions class="action-dialog">
    <button mat-button class="btn-quaternary" (click)="onCancel()">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      mat-raised-button
      class="btn-primary"
      [disabled]="form.invalid"
      (click)="updateInsuranceCalculation()"
    >
      {{ 'common.update' | translate }}
    </button>
  </mat-dialog-actions>
