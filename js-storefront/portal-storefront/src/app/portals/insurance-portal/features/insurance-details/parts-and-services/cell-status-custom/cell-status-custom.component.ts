import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { UserService } from '../../../../../../core/services/user';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-cell-status-custom',
  standalone: true,
  imports: [MatIconModule],
  templateUrl: './cell-status-custom.component.html',
  styleUrls: ['./cell-status-custom.component.scss']
})
export class CellStatusCustomComponent implements ICellRendererAngularComp {
  @Input() rowIndex!: number;

  @Output() close = new EventEmitter<void>();
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
    
  params!: any;
  icon: string = '';

  agInit(params: any): void {
    this.params = params;
    this.icon = (params?.value === undefined) || (params?.value && params?.value?.code === undefined) ? 'ic-unapproved' : 
    (params?.value?.code === 'REJECTED' ? 'ic-rejected' : (params?.value?.code === 'APPROVED' ? 'ic-claim-approved' : 'ic-unapproved'));
  }

  refresh(params: any): boolean {
    return true;
  }
}

