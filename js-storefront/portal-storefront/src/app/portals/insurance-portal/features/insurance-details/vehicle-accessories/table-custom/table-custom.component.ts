import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { AgGridAngular } from 'ag-grid-angular';
import { AllCommunityModule, ColDef, ModuleRegistry, provideGlobalGridOptions } from 'ag-grid-community';


ModuleRegistry.registerModules([AllCommunityModule]);
provideGlobalGridOptions({ theme: "legacy"});
@Component({
  selector: 'app-table-custom',
  standalone: true,
  imports: [TranslateModule, AgGridAngular, CommonModule],
  templateUrl: './table-custom.component.html',
  styleUrls: ['./table-custom.component.scss'],
})
export class TableCustomComponent {
  @ViewChild('myGrid') grid!: AgGridAngular;
  @Input() titleHeader: string;
  @Input() rowData: any;
  @Input() colDefs: any;

  @Output() rowSelected = new EventEmitter();
  defaultColDef: ColDef<any> = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    suppressMovable: true,
  };

  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
  };

  handleGetRowSelected(event: any) {
    this.rowSelected.emit(this.grid.api.getSelectedRows());
  }
}
