<h2 class="title-dialog">
  {{
    "newVehicleInsurance.detail.insuranceInformation.updateInsuranceInformation"
      | translate
  }}
</h2>

<mat-dialog-content
  class="container-dialog"
  [class.loading]="loadingService.isLoading"
>
  <form [formGroup]="form">
    <div class="row">
      <div class="col-12">
        <app-dropdown-form-group
          [label]="
            'newVehicleInsurance.detail.insuranceInformation.scheme'
              | translate
          "
          [control]="form.controls.scheme"
          [options]="insuranceSchemeList"
          [required]="true"
        ></app-dropdown-form-group>
      </div>
      <div class="col-12">
        <app-dropdown-form-group
          [label]="
            'newVehicleInsurance.detail.insuranceInformation.typeOfTransaction.name'
              | translate
          "
          [control]="form.controls.typeTransaction"
          [options]="transactionTypeList"
          [required]="true"
        ></app-dropdown-form-group>
      </div>
      <div class="col-12">
        <app-dropdown-form-group
          [label]="
            'newVehicleInsurance.detail.insuranceInformation.typeOfInsurance.name'
              | translate
          "
          [control]="form.controls.typeInsurance"
          [options]="typeOfInsuranceList"
          [required]="true"
        ></app-dropdown-form-group>
      </div>
      <div class="col-12">
        <app-dropdown-form-group
          [label]="
            'newVehicleInsurance.detail.insuranceInformation.insuranceCoverage.name'
              | translate
          "
          [control]="form.controls.insuranceCoverage"
          [options]="insuranceCoverageList"
        ></app-dropdown-form-group>
      </div>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>
  <button
    class="btn-primary"
    [disabled]="form.invalid"
    (click)="updateCustomerInformation()"
  >
    {{ "common.update" | translate }}
  </button>
</mat-dialog-actions>
