@import "../../../../../../../styles/abstracts/variables";

.errors-message {
  color: #eb0a1e;
  font-size: 12px;
  position: absolute;
  margin-top: 4px;
}
.padding-bottom-5 {
  padding-bottom: 5px;
}
.non-padding {
  padding: 0 20px 0 0 !important;
}

.installment-fields-wrapper {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  height: 33px;
  .installment-fields {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 35px;
  }
}

.checkbox-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0 0 30px;

  .mdc-form-field>label {
    font-size: 16px;
    font-weight: 600;
  }
}

.insurance-calculation {
  table-layout: fixed;
  width: 100%;
  border-spacing: 0;
  .align-right {
    text-align: right;
    padding-right: 17px;
  }

  &__input {
    padding-right: 20px;
  }
  tr {
    th {
      text-align: left;
      font-size: 16px;
      font-weight: 600;
      color: #101010;
    }
    td {
      padding-top: 20px;
      position: relative;
    }
  }
}

.bold-label {
  font-weight: bold;
}

.update-insurance-calculation-title {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 35px;
  margin-top: 34px;
  text-align: center;
}

.update-insurance-calculation-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 55px;

  .mat-divider {
    margin-top: 35px;
    border-top-color: #808080;
  }

  .update-insurance-calculation__table {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 10px;
  }

  .update-insurance-calculation__left {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .update-insurance-calculation__right {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .update-insurance-calculation__header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #f8f8f8;
    font-weight: bold;
    padding: 10px;
    text-align: center;
  }

  .update-insurance-calculation__row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
  }

  .update-insurance-calculation__col {
    padding: 8px;
    text-align: center;
  }

  .update-insurance-calculation__label {
    font-weight: bold;
    padding: 8px 0;
  }

  input {
    width: 100%;
    height: 38px;
    line-height: 1;
    padding: 6px 0;
    color: #101010;
    border: none;
    border-bottom: 1px solid rgba(16, 16, 16, 0.2);
    font-size: 16px;
    box-sizing: border-box;
    outline: none;

    &:disabled,
    &:read-only {
      color: $text-color-4;
      cursor: not-allowed;
      background-color: $bg-color-10;
      padding-left: 7px;
    }
  }

  /* Hide spinner arrows for number inputs */
  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
    appearance: textfield;
    /* Standard */
  }

  /* Hide spinner arrows for WebKit browsers (Chrome, Safari, Edge) */
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.action-dialog {
  justify-content: center !important;
  gap: 20px;
}
