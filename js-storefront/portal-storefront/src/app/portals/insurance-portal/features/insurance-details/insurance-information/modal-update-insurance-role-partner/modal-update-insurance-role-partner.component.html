
<h2 class="title-dialog">
  {{ "newVehicleInsurance.detail.insuranceInformation.updateInsuranceInformation" | translate }}
</h2>

<mat-dialog-content
  class="container-dialog"
  [class.loading]="loadingService.isLoading"
>
  @if(isComprehensiveCompulsoryTPL) {
    <form [formGroup]="form" class="container-item-box">
      <div class="container-item">
        <div class="container-item-title">{{'newVehicleInsurance.detail.insuranceInformation.comprehensiveInsuranceInformation' | translate}}</div>
        <div class="row">
          <div class="col-12">
            <app-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.compInsurancePolicyCode' | translate"
              [control]="form.controls.compPolicyCode"
              [placeholder]="'newVehicleInsurance.detail.insuranceInformation.placeholderPleaseEnter' | translate"
            ></app-form-group>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <app-date-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.compDateofPolicyInsurance' | translate"
              [placeholder]="'mm/dd/yyyy'"
              [control]="form.controls.compDateOfPolicy"
              controlId="compDateOfPolicy"
              (onDateChangeEvent)="onDateOfPolicyChange($event)"
            ></app-date-form-group>
            <div class="date-of-policy-warning">{{ dateOfPolicyWarning }}</div>
          </div>
          <div class="col-6">
            <app-date-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.compInsuranceExpiryDate' | translate"
              [placeholder]="'mm/dd/yyyy'"
              [control]="form.controls.compExpireDate"
              [errorMessage]="'newVehicleInsurance.detail.insuranceInformation.beFutureDate' | translate"
              controlId="compExpireDate"
              (onDateChangeEvent)="onExpireDateChange($event, 'compExpireDate')"
            ></app-date-form-group>
          </div>
        </div>
      </div>
      <hr class="line-break" />
      <div class="container-item">
        <div class="container-item-title">{{'newVehicleInsurance.detail.insuranceInformation.compulsoryTPLInsuranceInformation' | translate}}</div>
        <div class="row">
          <div class="col-12">
            <app-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.ctplInsurancePolicyCode' | translate"
              [control]="form.controls.ctplPolicyCode"
              [placeholder]="'newVehicleInsurance.detail.insuranceInformation.placeholderPleaseEnter' | translate"
            ></app-form-group>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <app-date-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.ctplDateofPolicyInsurance' | translate"
              [placeholder]="'mm/dd/yyyy'"
              [control]="form.controls.ctplDateOfPolicy"
              controlId="ctplDateOfPolicy"
              (onDateChangeEvent)="onDateOfPolicyChange($event, true)"
            ></app-date-form-group>
            <div class="date-of-policy-warning">{{ dateOfPolicyCTPLWarning }}</div>
          </div>
          <div class="col-6">
            <app-date-form-group
              [label]="'newVehicleInsurance.detail.insuranceInformation.ctplInsuranceExpiryDate' | translate"
              [placeholder]="'mm/dd/yyyy'"
              [control]="form.controls.ctplExpireDate"
              [errorMessage]="(form.controls.ctplExpireDate.errors?.['ctplExpireDateInvalid']? 
                'newVehicleInsurance.detail.insuranceInformation.ctplExpireDateInvalid' 
                : 'newVehicleInsurance.detail.insuranceInformation.beFutureDate') | translate"
              controlId="ctplExpireDate"
              (onDateChangeEvent)="onExpireDateChange($event, 'ctplExpireDate')"
            ></app-date-form-group>
          </div>
        </div>
      </div>
    </form>
  } @else {
    <form [formGroup]="form">
      <div class="row">
        <div class="col-12">
          <app-form-group
            [label]="'newVehicleInsurance.detail.insuranceInformation.insurancePolicyCode' | translate"
            [control]="form.controls.policyCode"
            [placeholder]="'newVehicleInsurance.detail.insuranceInformation.placeholderInsurancePolicyCode' | translate"
          ></app-form-group>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <app-date-form-group
            [label]="'newVehicleInsurance.detail.insuranceInformation.dateOfPolicyInsurance' | translate"
            [placeholder]="'mm/dd/yyyy'"
            [control]="form.controls.dateOfPolicy"
            controlId="dateOfPolicy"
            (onDateChangeEvent)="onDateOfPolicyChange($event)"
          ></app-date-form-group>
          <div class="date-of-policy-warning">{{ dateOfPolicyWarning }}</div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <app-date-form-group
            [label]="'newVehicleInsurance.detail.insuranceInformation.insuranceExpiryDate' | translate"
            [placeholder]="'mm/dd/yyyy'"
            [control]="form.controls.expireDate"
            [errorMessage]="'newVehicleInsurance.detail.insuranceInformation.beFutureDate' | translate"
            controlId="expireDate"
            (onDateChangeEvent)="onExpireDateChange($event, 'expireDate')"
          ></app-date-form-group>
        </div>
      </div>
    </form>
  }
</mat-dialog-content>
<mat-dialog-actions class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>
  <button
    class="btn-primary"
    [disabled]="form.invalid"
    (click)="updateCustomerInformation()"
  >
    {{ "common.update" | translate }}
  </button>
</mat-dialog-actions>

