@import "../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/components/ag-grid";
.table-custom {
  border-bottom: 1px solid var(--Primary-Black, #101010);
  padding-bottom: 10px;
}
::ng-deep {
  .detail-content-table {
    .text-right {
        .ag-header-cell-text {
            width: 100%;
            text-align: right;
        }
    }
    
    .ag-header-cell-text {
      color: var(--Primary-Black, #101010);
      font-family: "Toyota Type";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
    .ag-header-cell-label {
      // padding: 15px;
    }

    .header-right {
      .ag-header-cell-comp-wrapper .ag-header-cell-label {
        justify-content: flex-end !important;
      }
    }

    .header-center {
      .ag-header-cell-comp-wrapper .ag-header-cell-label {
        justify-content: center !important;
      }
    }
    .ag-cell {
      display: flex;
      align-items: center;
      // padding: 15px;
      text-overflow: ellipsis;
      overflow: hidden; 
      white-space: nowrap;
      &.cell-right {
        justify-content: flex-end;
      }
      &.cell-center {
        justify-content: center;
      }
      span {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .custom-grid .ag-row:nth-child(odd) {
      background-color: #f5f5f5;
    }

    .ag-tooltip{
        background-color: #ffffff;
        color: #101010;
        border-radius: 2px;
        padding: 5px;
        border-width: 1px;
        border-style: solid;
        border-color: #101010;
     }
  }
  .custom-grid {
    --ag-icon-size: 18px !important;
    --ag-font-family: $text-font-stack !important;
    --ag-background-color: transparent !important;
    width: 100%;
    padding: 0 0 15px;

    font-family: $text-font-stack;
    
    .ag-root-wrapper {
      border: none;
    }

    .ag-row:nth-child(odd) {
      background-color: $bg-color-10;

      &.ag-row-hover:not(.ag-full-width-row)::before, &.ag-row-hover.ag-full-width-row.ag-row-group::before {
        background-color: $bg-color-10;
      }
    }

    .ag-row:nth-child(even) {
      background-color: $white-bg;

      &.ag-row-hover:not(.ag-full-width-row)::before, &.ag-row-hover.ag-full-width-row.ag-row-group::before {
        background-color: $white-bg;
      }
    }

    .ag-header-container {
      background-color: $white-bg;
      font-weight: $fw600;
      font-size: $fs16;
    }

    .ag-header {
      border: none;
    }

    .ag-header-row {
      font-weight: 600;
    }

    .ag-row {
      border: none;
      line-height: 52px;

      &:hover {
        background-color: unset !important;
      }
    }

    .can-click {
      &:hover {
        cursor: pointer;
        text-decoration: underline;
      }
    }

    .action-grid {
      app-icon-cell-renderer {
        @include action-grid;
      }
    }

    .ag-cell {
      line-height: 52px;
    }

    .ag-cell-wrap-text {
      line-height: 20px !important;
      padding-top: 11px;
      padding-bottom: 11px;
      display: flex;
      align-items: center;
      .value-tag {
        @include value-tag;
        &.red-tag {
          @include red-tag;
        }
  
        &.grey-tag {
          @include grey-tag;
        }
  
        &.yellow-tag {
          @include yellow-tag;
        }
      }
    }

    .value-tag {
      > span {
        padding: 4px 8px;
        background-color: $bg-color-2;
        border: 1px solid rgba(16, 16, 16, 0.05);
      }
    }

    .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
      border: none !important;
    }

    .cell-word-wrap {
      word-wrap: break-word;
      white-space: normal;
    }
  }

  .mobile-container {
    padding: 20px 0;

    .row-container {
      padding: 20px;

      display: flex;
      flex-direction: column;
      gap: 12px;

      &--row-gray {
        background-color: $bg-color-10;
      }

      &--row-white {
        background-color: $white-bg;
      }

      .row-item {
        display: flex;
        justify-content: space-between;
        min-height: 28px;

        &__header {
          font-weight: $fw600;
        }

        .value-tag {
          padding: 4px 8px;
          background-color: $bg-color-2;
        }

        .action-grid {
          display: flex;
          gap: 5px;
          align-items: center;

          :first-child {
            width: 16px;
            height: 16px;
            object-fit: unset;
          }

          @include action-grid;
        }
      }
    }
  }

  .ag-checkbox-input-wrapper:focus-within, .ag-checkbox-input-wrapper:active {
    box-shadow: unset !important
  }

  .ag-checked {
    &::after {
        color: #101010 !important;
    }
  }
}

:host {
    .no-data-message {
        text-align: center;
        color: #101010;
        font-size: 20px;
        padding: 20px;
        opacity: 0.5;
    }
}