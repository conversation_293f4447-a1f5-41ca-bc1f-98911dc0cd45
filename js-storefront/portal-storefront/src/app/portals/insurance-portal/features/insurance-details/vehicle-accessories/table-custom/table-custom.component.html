<div class="detail-content detail-content-table">
    <ng-container *ngIf="rowData && rowData.length > 0; else noData">
        <ag-grid-angular #myGrid class="ag-theme-quartz custom-grid" [rowData]="rowData" [columnDefs]="colDefs"
            [domLayout]="'autoHeight'" [defaultColDef]="defaultColDef" [gridOptions]="gridOptions"
            (selectionChanged)="handleGetRowSelected($event)">
        </ag-grid-angular>
    </ng-container>

    <ng-template #noData>
        <div class="no-data-message">
            {{ 'vehicleAccessories.noData' | translate }}
        </div>
    </ng-template>
</div>