import { ResponsePaging } from './paging.interface';
import { Vehicle } from './ticket-detail.interface';

export interface ItemSubscription {
  endDateFormat: string;
  loanStatusText: string;
  paymentStatusText: string;
  subscriptionStatusText: string;
  vehicleType: string;
  vin: string;
}

export interface ListItemSubscription {
  pagination: ResponsePaging;
  items: ItemSubscription[];
}

export interface SubscriptionDetail {
  loan: {
    endDate: string;
    id: string;
    startDate: string;
    status: {
      code: string;
      name: string;
    };
  };
  vehicle: Vehicle;
  subscriptions: ItemSubscription;
  isValidForSubscriptionEnable: boolean;
}
