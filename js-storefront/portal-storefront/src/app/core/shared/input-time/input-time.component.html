<div class="input-time">
  <div class="form-group">
    <label class="form-group__label">
      {{ label | translate }}<span *ngIf="required" class="required">*</span>
    </label>
    <div
      class="item-hours form-group__input"
      (click)="controlHH?.value ? false : inputHH.focus()"
    >
      <input
        class="form-control item-hours__time-hh"
        type="text"
        [formControl]="controlHH"
        #inputHH
        (input)="onInputTimeHH($event)"
        maxlength="3"
        appInputNumber
      />
      <span class="item-hours__separator">:</span>
      <input
        type="text"
        (input)="onInputTimeMM($event)"
        maxlength="3"
        appInputNumber
        [formControl]="controlMM"
        #inputHH
        class="form-control item-hours__time-mm"
      />
      <mat-icon svgIcon="ic-clock"></mat-icon>
    </div>
  </div>
  <div
    *ngIf="isShowError && ((controlHH?.hasError('required') && controlHH?.touched) || (controlMM?.hasError('required') && controlMM?.touched))"
    class="form-group__error"
  >
    {{ errorMessage || "validation.fillInData" | translate }}
  </div>
</div>
