import { Directive, ElementRef, HostListener, Input } from '@angular/core';
@Directive({
  selector: '[appInputNumber]',
  standalone: true,
})
export class InputTermCalculateDirective {
    @Input('onlyNumber') onlyNumber: boolean;
  constructor(el: ElementRef) {}

  @HostListener('keydown', ['$event']) onKeyPress(event: any) {
    if (this.onlyNumber) {
        this.integerOnly(event);
    }
  }

  @HostListener('paste', ['$event']) blockPaste(e: ClipboardEvent) {
    const pasted = e.clipboardData?.getData('text') || '';
    if (!(/^[0-9]*$/.test(pasted))) {
      e.preventDefault();
    }
  }

  @HostListener('drop', ['$event']) blockDrop(e: DragEvent) {
    const pasted = e.dataTransfer?.getData('text') || '';
    if (!(/^[0-9]*$/.test(pasted))) {
      e.preventDefault();
    }
  }

  integerOnly(event: any) {
    const e = event;
    if (e.key === 'Tab' || e.key === 'TAB') {
      return;
    }
    if (
      [46, 8, 9, 27, 13, 110].indexOf(e.keyCode) !== -1 ||
      // Allow: Ctrl+A
      (e.keyCode === 65 && e.ctrlKey === true) ||
      // Allow: Ctrl+C
      (e.keyCode === 67 && e.ctrlKey === true) ||
      // Allow: Ctrl+V
      (e.keyCode === 86 && e.ctrlKey === true) ||
      // Allow: Ctrl+X
      (e.keyCode === 88 && e.ctrlKey === true)
    ) {
      // let it happen, don't do anything
      return;
    }
    if (
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'].indexOf(e.key) === -1
    ) {
      e.preventDefault();
    }
  }
}
