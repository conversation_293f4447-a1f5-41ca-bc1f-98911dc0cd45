import { inject, Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { RIStatus } from '../../enums';

@Injectable()
export class RemoteImmobilizerService {
  #http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getImmobilizerStatus(vin: string): Observable<{
    vin: string;
    status: RIStatus;
    performedBy: string;
    immobilizerApproveDate: Date;
  }> {
    const path = `${this.prefixEnv}iot-portal/immobilizer/${vin}/status`;
    return this.#http.get<{
      vin: string;
      status: RIStatus;
      performedBy: string;
      immobilizerApproveDate: Date;
    }>(path);
  }

  disableImmobilizer(vin: string): Observable<{
    vin: string;
    result: string;
    status: RIStatus;
  }> {
    const path = `${this.prefixEnv}iot-portal/immobilizer/${vin}/disable`;
    return this.#http.put<{
      vin: string;
      result: string;
      status: RIStatus;
    }>(path, {});
  }

  
  enableImmobilizer(vin: string): Observable<{
    vin: string;
    result: string;
    status: RIStatus;
  }> {
    const path = `${this.prefixEnv}iot-portal/immobilizer/${vin}/enable`;
    return this.#http.put<{
      vin: string;
      result: string;
      status: RIStatus;
    }>(path, {});
  }

  synchRegionalStatus(vin: string): Observable<{
    vin: string;
    status: RIStatus;
    performedBy: string;
    immobilizerApproveDate: Date;
  }> {
    const path = `${this.prefixEnv}iot-portal/immobilizer/${vin}/synchRegionalStatus`;
    return this.#http.get<{
      vin: string;
      status: RIStatus;
      performedBy: string;
      immobilizerApproveDate: Date;
    }>(path);
  }
}
