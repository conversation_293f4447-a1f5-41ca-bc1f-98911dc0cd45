spring:
  config:
    import: "optional:configserver:http://config-service:8088"
    activate:
      on-profile: docker
---
spring:
  application:
    name: claim-service
  config:
    import: "optional:configserver:http://localhost:8088"

---
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8180/realms/tmaim
  datasource:
    url: ********************************************
    username: tmp_claim
    password: 123dzo!
    driver-class-name: org.postgresql.Driver
  jpa:
      database-platform: org.hibernate.dialect.PostgreSQLDialect
      hibernate:
        ddl-auto: update
      show-sql: true
      properties:
        hibernate:
          format_sql: true
          enable_lazy_load_no_trans: true
  sql:
      init:
        mode: always
  kafka:
    bootstrap-servers: ***********:9092
keycloak:
  realm: tmp-claim
  client:
    client-id: tmp-claim-master
    client-secret: 7M3jO4RUEfbpUtd9bz0zA9H8WvUeUbOx


zipkin:
  tracing:
    endpoint: http://***********:9411/api/v2/spans
    
claim:
  code:
    prefix: CLM

tmp:
  claim:
    portal:
      claims:
        pagesize: 30