package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnumWsDTO {

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("type")
    private String type;

    public static EnumWsDTO fromRebateClaimStatus(RebateClaimStatus status) {
        if (status == null) {
            return null;
        }
        
        return EnumWsDTO.builder()
                .code(status.name())
                .name(formatStatusName(status.name()))
                .type("RebateClaimStatus")
                .build();
    }

    public static EnumWsDTO fromEnum(Enum<?> enumValue, String enumType) {
        if (enumValue == null) {
            return null;
        }
        
        return EnumWsDTO.builder()
                .code(enumValue.name())
                .name(formatEnumName(enumValue.name()))
                .type(enumType)
                .build();
    }

    private static String formatStatusName(String statusCode) {
        // Convert enum name to human-readable format
        return statusCode.replace("_", " ")
                .toLowerCase()
                .replaceAll("\\b\\w", m -> m.group().toUpperCase());
    }

    private static String formatEnumName(String enumName) {
        // Convert enum name to human-readable format
        return enumName.replace("_", " ")
                .toLowerCase()
                .replaceAll("\\b\\w", m -> m.group().toUpperCase());
    }
}
