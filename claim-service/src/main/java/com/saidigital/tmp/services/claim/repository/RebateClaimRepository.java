package com.saidigital.tmp.services.claim.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.model.RebateClaim;

@Repository
public interface RebateClaimRepository extends JpaRepository<RebateClaim, String> {

    /**
     * Find all rebate claims with optional filtering by dealer and status
     * Results are ordered by creation time in descending order
     */
    @Query("SELECT rc FROM RebateClaim rc " +
           "WHERE (:dealer IS NULL OR :dealer = '' OR rc.dealer = :dealer) " +
           "AND (:status IS NULL OR rc.status = :status) " +
           "ORDER BY rc.createdTime DESC")
    Page<RebateClaim> findAllRebateClaimsWithFilters(
            @Param("dealer") String dealer,
            @Param("status") RebateClaimStatus status,
            Pageable pageable);

    /**
     * Find rebate claims by dealer
     */
    List<RebateClaim> findByDealerOrderByCreatedTimeDesc(String dealer);

    /**
     * Find rebate claims by status
     */
    List<RebateClaim> findByStatusOrderByCreatedTimeDesc(RebateClaimStatus status);

    /**
     * Find rebate claims by dealer and status
     */
    List<RebateClaim> findByDealerAndStatusOrderByCreatedTimeDesc(String dealer, RebateClaimStatus status);

    /**
     * Count total rebate claims with filters
     */
    @Query("SELECT COUNT(rc) FROM RebateClaim rc " +
           "WHERE (:dealer IS NULL OR :dealer = '' OR rc.dealer = :dealer) " +
           "AND (:status IS NULL OR rc.status = :status)")
    long countRebateClaimsWithFilters(
            @Param("dealer") String dealer,
            @Param("status") RebateClaimStatus status);
}
