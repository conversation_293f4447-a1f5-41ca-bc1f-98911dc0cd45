package com.saidigital.tmp.services.claim.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebateClaimResponseDTO {

    @JsonProperty("id")
    private String code;

    @JsonProperty("status")
    private StatusDTO status;

    @JsonProperty("claimableAmount")
    private Double claimableAmount;

    @JsonProperty("dealer")
    private String dealer;

    @JsonProperty("createdDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdDate;

    @JsonProperty("lastUpdatedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date lastUpdatedDate;

    @JsonProperty("lastUpdatedBy")
    private String lastUpdatedBy;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("dmcmReference")
    private String dmcmReference;

    @JsonProperty("sbReference")
    private String sbReference;

    @JsonProperty("sapDocNo")
    private String sapDocNo;

    @JsonProperty("clearingDocNo")
    private String clearingDocNo;

    @JsonProperty("orNumber")
    private String orNumber;

    @JsonProperty("exported")
    private Boolean exported;

    @JsonProperty("requiredDoc")
    private String requiredDoc;

    @JsonProperty("generalConfigVersion")
    private Integer generalConfigVersion;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusDTO {
        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        public static StatusDTO fromEnum(RebateClaimStatus status) {
            if (status == null) {
                return null;
            }
            return StatusDTO.builder()
                    .code(status.name())
                    .name(formatStatusName(status.name()))
                    .build();
        }

        private static String formatStatusName(String statusCode) {
            // Convert enum name to human-readable format
            return statusCode.replace("_", " ")
                    .toLowerCase()
                    .replaceAll("\\b\\w", m -> m.group().toUpperCase());
        }
    }
}
