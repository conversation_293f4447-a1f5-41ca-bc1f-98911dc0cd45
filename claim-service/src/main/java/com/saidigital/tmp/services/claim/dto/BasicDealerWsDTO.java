package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicDealerWsDTO {

    @JsonProperty("name")
    private String name;

    @JsonProperty("displayName")
    private String displayName;

    @JsonProperty("code")
    private String code;

    @JsonProperty("uid")
    private String uid;

    public static BasicDealerWsDTO fromDealerCode(String dealerCode) {
        if (dealerCode == null || dealerCode.trim().isEmpty()) {
            return null;
        }
        
        return BasicDealerWsDTO.builder()
                .name(dealerCode)
                .displayName(formatDealerDisplayName(dealerCode))
                .code(dealerCode)
                .uid(dealerCode)
                .build();
    }

    private static String formatDealerDisplayName(String dealerCode) {
        // This should ideally come from a dealer service or lookup table
        // For now, we'll format it as "Dealer {code}"
        return "Dealer " + dealerCode;
    }
}
