package com.saidigital.tmp.services.claim.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PagedRebateClaimResponse {

    @JsonProperty("type")
    private String type;

    @JsonProperty("pagination")
    private PaginationDTO pagination;

    @JsonProperty("items")
    private List<RebateClaimResponseDTO> items;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaginationDTO {
        @JsonProperty("currentPage")
        private int currentPage;

        @JsonProperty("pageSize")
        private int pageSize;

        @JsonProperty("totalPages")
        private int totalPages;

        @JsonProperty("totalResults")
        private long totalResults;

        @JsonProperty("hasNext")
        private boolean hasNext;

        @JsonProperty("hasPrevious")
        private boolean hasPrevious;

        @JsonProperty("sort")
        private String sort;
    }

    public static PagedRebateClaimResponse create(
            List<RebateClaimResponseDTO> items,
            int currentPage,
            int pageSize,
            long totalResults,
            String sort) {
        
        int totalPages = (int) Math.ceil((double) totalResults / pageSize);
        
        PaginationDTO pagination = PaginationDTO.builder()
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .totalResults(totalResults)
                .hasNext(currentPage < totalPages - 1)
                .hasPrevious(currentPage > 0)
                .sort(sort)
                .build();

        return PagedRebateClaimResponse.builder()
                .type("rebateClaimSearchPageWsDTO")
                .pagination(pagination)
                .items(items)
                .build();
    }
}
