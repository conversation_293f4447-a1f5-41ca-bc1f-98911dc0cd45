package com.saidigital.tmp.services.claim.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.saidigital.tmp.services.claim.dto.PagedRebateClaimResponse;
import com.saidigital.tmp.services.claim.dto.RebateClaimResponseDTO;
import com.saidigital.tmp.services.claim.service.RebateClaimService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/rebate-claims")
@Tag(name = "Rebate Claims", description = "Rebate Claims Management API")
public class RebateClaimController {

    @Autowired
    private RebateClaimService rebateClaimService;

    @Value("${tmp.claim.portal.claims.pagesize:30}")
    private int defaultPageSize;

    @GetMapping
    @Operation(summary = "Get all rebate claims", 
               description = "Retrieve paginated list of rebate claims with optional filtering by dealer and status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved rebate claims"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<PagedRebateClaimResponse> getRebateClaims(
            @Parameter(description = "Dealer code filter", example = "0030")
            @RequestParam(name = "dealer", required = false, defaultValue = "") String dealer,
            
            @Parameter(description = "Status filter", example = "MARKETING_VALIDATED")
            @RequestParam(name = "status", required = false, defaultValue = "") String status,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(value = "page", defaultValue = "0") int page,
            
            @Parameter(description = "Page size", example = "30")
            @RequestParam(value = "size", required = false) Integer size,
            
            @Parameter(description = "Sort criteria (property,direction)", example = "createdTime,desc")
            @RequestParam(value = "sort", required = false) String sort) {

        // Use default page size if not provided
        int pageSize = (size != null) ? size : defaultPageSize;

        // Validate page parameters
        if (page < 0) {
            page = 0;
        }
        if (pageSize <= 0) {
            pageSize = defaultPageSize;
        }
        if (pageSize > 100) {
            pageSize = 100; // Maximum page size limit
        }

        PagedRebateClaimResponse response = rebateClaimService.getAllRebateClaims(
                dealer, status, page, pageSize, sort);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{code}")
    @Operation(summary = "Get rebate claim by code", 
               description = "Retrieve a specific rebate claim by its code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved rebate claim"),
        @ApiResponse(responseCode = "404", description = "Rebate claim not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<RebateClaimResponseDTO> getRebateClaimByCode(
            @Parameter(description = "Rebate claim code", example = "0030-RC-12345")
            @PathVariable String code) {

        RebateClaimResponseDTO claim = rebateClaimService.getRebateClaimByCode(code);
        
        if (claim == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(claim);
    }

    @GetMapping("/count")
    @Operation(summary = "Get rebate claims count", 
               description = "Get total count of rebate claims with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved count"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Long> getRebateClaimsCount(
            @Parameter(description = "Dealer code filter", example = "0030")
            @RequestParam(name = "dealer", required = false, defaultValue = "") String dealer,
            
            @Parameter(description = "Status filter", example = "MARKETING_VALIDATED")
            @RequestParam(name = "status", required = false, defaultValue = "") String status) {

        long count = rebateClaimService.getTotalRebateClaimsCount(dealer, status);
        return ResponseEntity.ok(count);
    }
}
