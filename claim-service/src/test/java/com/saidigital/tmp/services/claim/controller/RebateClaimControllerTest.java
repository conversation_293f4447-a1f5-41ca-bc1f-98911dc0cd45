package com.saidigital.tmp.services.claim.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.saidigital.tmp.services.claim.dto.PagedRebateClaimResponse;
import com.saidigital.tmp.services.claim.dto.RebateClaimResponseDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.service.RebateClaimService;

@WebMvcTest(RebateClaimController.class)
public class RebateClaimControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RebateClaimService rebateClaimService;

    @Test
    public void testGetRebateClaims_Success() throws Exception {
        // Prepare test data
        RebateClaimResponseDTO claim1 = RebateClaimResponseDTO.builder()
                .code("0030-CLM-001")
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.MARKETING_VALIDATED))
                .claimableAmount(1000.0)
                .dealer("0030")
                .createdDate(new Date())
                .lastUpdatedDate(new Date())
                .lastUpdatedBy("testuser")
                .build();

        RebateClaimResponseDTO claim2 = RebateClaimResponseDTO.builder()
                .code("0030-CLM-002")
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.RFP_PROCESSING))
                .claimableAmount(2000.0)
                .dealer("0030")
                .createdDate(new Date())
                .lastUpdatedDate(new Date())
                .lastUpdatedBy("testuser")
                .build();

        PagedRebateClaimResponse mockResponse = PagedRebateClaimResponse.create(
                Arrays.asList(claim1, claim2),
                0, // currentPage
                30, // pageSize
                2L, // totalResults
                "createdTime,desc" // sort
        );

        // Mock service call
        when(rebateClaimService.getAllRebateClaims(anyString(), anyString(), anyInt(), anyInt(), anyString()))
                .thenReturn(mockResponse);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims")
                .param("dealer", "0030")
                .param("status", "MARKETING_VALIDATED")
                .param("page", "0")
                .param("size", "30")
                .param("sort", "createdTime,desc")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.type").value("rebateClaimSearchPageWsDTO"))
                .andExpect(jsonPath("$.pagination.currentPage").value(0))
                .andExpect(jsonPath("$.pagination.pageSize").value(30))
                .andExpect(jsonPath("$.pagination.totalResults").value(2))
                .andExpect(jsonPath("$.items").isArray())
                .andExpect(jsonPath("$.items.length()").value(2))
                .andExpect(jsonPath("$.items[0].id").value("0030-CLM-001"))
                .andExpect(jsonPath("$.items[0].dealer").value("0030"))
                .andExpect(jsonPath("$.items[0].claimableAmount").value(1000.0))
                .andExpect(jsonPath("$.items[0].status.code").value("MARKETING_VALIDATED"));
    }

    @Test
    public void testGetRebateClaimByCode_Success() throws Exception {
        // Prepare test data
        RebateClaimResponseDTO claim = RebateClaimResponseDTO.builder()
                .code("0030-CLM-001")
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.MARKETING_VALIDATED))
                .claimableAmount(1000.0)
                .dealer("0030")
                .createdDate(new Date())
                .lastUpdatedDate(new Date())
                .lastUpdatedBy("testuser")
                .build();

        // Mock service call
        when(rebateClaimService.getRebateClaimByCode("0030-CLM-001")).thenReturn(claim);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims/0030-CLM-001")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("0030-CLM-001"))
                .andExpect(jsonPath("$.dealer").value("0030"))
                .andExpect(jsonPath("$.claimableAmount").value(1000.0))
                .andExpect(jsonPath("$.status.code").value("MARKETING_VALIDATED"));
    }

    @Test
    public void testGetRebateClaimByCode_NotFound() throws Exception {
        // Mock service call to return null
        when(rebateClaimService.getRebateClaimByCode("nonexistent")).thenReturn(null);

        // Perform request and verify 404 response
        mockMvc.perform(get("/api/rebate-claims/nonexistent")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testGetRebateClaimsCount_Success() throws Exception {
        // Mock service call
        when(rebateClaimService.getTotalRebateClaimsCount(anyString(), anyString())).thenReturn(5L);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims/count")
                .param("dealer", "0030")
                .param("status", "MARKETING_VALIDATED")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("5"));
    }

    @Test
    public void testGetRebateClaims_WithDefaultParameters() throws Exception {
        // Prepare test data
        PagedRebateClaimResponse mockResponse = PagedRebateClaimResponse.create(
                Arrays.asList(),
                0, // currentPage
                30, // pageSize (default)
                0L, // totalResults
                null // sort
        );

        // Mock service call
        when(rebateClaimService.getAllRebateClaims(anyString(), anyString(), anyInt(), anyInt(), any()))
                .thenReturn(mockResponse);

        // Perform request without parameters
        mockMvc.perform(get("/api/rebate-claims")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.type").value("rebateClaimSearchPageWsDTO"))
                .andExpect(jsonPath("$.pagination.currentPage").value(0))
                .andExpect(jsonPath("$.pagination.pageSize").value(30));
    }
}
