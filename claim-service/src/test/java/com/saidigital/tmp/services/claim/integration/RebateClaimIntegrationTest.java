package com.saidigital.tmp.services.claim.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Date;

import org.junit.jupiter.api.Test;

import com.saidigital.tmp.services.claim.dto.RebateClaimResponseDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.model.RebateClaim;

/**
 * Integration test to verify the basic functionality of our rebate claims migration
 * without requiring a full Spring context
 */
public class RebateClaimIntegrationTest {

    @Test
    public void testRebateClaimEntityCreation() {
        // Test that we can create a RebateClaim entity
        Date now = new Date();
        
        RebateClaim claim = RebateClaim.builder()
                .code("0030-CLM-001")
                .status(RebateClaimStatus.MARKETING_VALIDATED)
                .claimableAmount(1000.0)
                .dealer("0030")
                .createdBy("testuser")
                .updatedBy("testuser")
                .referenceNumber("REF001")
                .dmcmReference("DMCM001")
                .sbReference("SB001")
                .exported(false)
                .generalConfigVersion(1)
                .build();
        
        claim.setCreatedTime(now);
        claim.setUpdatedTime(now);

        // Verify entity creation
        assertNotNull(claim);
        assertEquals("0030-CLM-001", claim.getCode());
        assertEquals(RebateClaimStatus.MARKETING_VALIDATED, claim.getStatus());
        assertEquals(1000.0, claim.getClaimableAmount());
        assertEquals("0030", claim.getDealer());
        assertEquals("testuser", claim.getCreatedBy());
        assertEquals("REF001", claim.getReferenceNumber());
        assertEquals(false, claim.isExported());
        assertEquals(Integer.valueOf(1), claim.getGeneralConfigVersion());
    }

    @Test
    public void testRebateClaimDTOCreation() {
        // Test that we can create a response DTO
        RebateClaimResponseDTO dto = RebateClaimResponseDTO.builder()
                .code("0030-CLM-001")
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.MARKETING_VALIDATED))
                .claimableAmount(1000.0)
                .dealer("0030")
                .createdDate(new Date())
                .lastUpdatedDate(new Date())
                .lastUpdatedBy("testuser")
                .referenceNumber("REF001")
                .dmcmReference("DMCM001")
                .sbReference("SB001")
                .exported(false)
                .generalConfigVersion(1)
                .build();

        // Verify DTO creation
        assertNotNull(dto);
        assertEquals("0030-CLM-001", dto.getCode());
        assertEquals("MARKETING_VALIDATED", dto.getStatus().getCode());
        assertEquals("Marketing Validated", dto.getStatus().getName());
        assertEquals(1000.0, dto.getClaimableAmount());
        assertEquals("0030", dto.getDealer());
        assertEquals("testuser", dto.getLastUpdatedBy());
        assertEquals("REF001", dto.getReferenceNumber());
        assertEquals(false, dto.getExported());
        assertEquals(Integer.valueOf(1), dto.getGeneralConfigVersion());
    }

    @Test
    public void testStatusDTOConversion() {
        // Test status enum to DTO conversion
        RebateClaimResponseDTO.StatusDTO statusDTO = 
                RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.MARKETING_VALIDATED);
        
        assertNotNull(statusDTO);
        assertEquals("MARKETING_VALIDATED", statusDTO.getCode());
        assertEquals("Marketing Validated", statusDTO.getName());

        // Test another status
        statusDTO = RebateClaimResponseDTO.StatusDTO.fromEnum(RebateClaimStatus.RFP_PROCESSING);
        assertNotNull(statusDTO);
        assertEquals("RFP_PROCESSING", statusDTO.getCode());
        assertEquals("Rfp Processing", statusDTO.getName());

        // Test null status
        statusDTO = RebateClaimResponseDTO.StatusDTO.fromEnum(null);
        assertEquals(null, statusDTO);
    }

    @Test
    public void testAllRebateClaimStatuses() {
        // Verify all enum values can be converted
        for (RebateClaimStatus status : RebateClaimStatus.values()) {
            RebateClaimResponseDTO.StatusDTO statusDTO = 
                    RebateClaimResponseDTO.StatusDTO.fromEnum(status);
            
            assertNotNull(statusDTO);
            assertEquals(status.name(), statusDTO.getCode());
            assertNotNull(statusDTO.getName());
        }
    }

    @Test
    public void testEntityToDTOConversion() {
        // Test manual conversion logic (similar to what the service does)
        Date now = new Date();
        
        RebateClaim claim = RebateClaim.builder()
                .code("0030-CLM-002")
                .status(RebateClaimStatus.RFP_PROCESSING)
                .claimableAmount(2500.0)
                .dealer("0030")
                .createdBy("admin")
                .updatedBy("admin")
                .referenceNumber("REF002")
                .dmcmReference("DMCM002")
                .sbReference("SB002")
                .sapDocNo("SAP002")
                .clearingDocNo("CLR002")
                .orNumber("OR002")
                .exported(true)
                .requiredDoc("Document required")
                .generalConfigVersion(2)
                .build();
        
        claim.setCreatedTime(now);
        claim.setUpdatedTime(now);

        // Convert to DTO (manual conversion like in service)
        RebateClaimResponseDTO dto = RebateClaimResponseDTO.builder()
                .code(claim.getCode())
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(claim.getStatus()))
                .claimableAmount(claim.getClaimableAmount())
                .dealer(claim.getDealer())
                .createdDate(claim.getCreatedTime())
                .lastUpdatedDate(claim.getUpdatedTime())
                .lastUpdatedBy(claim.getUpdatedBy())
                .referenceNumber(claim.getReferenceNumber())
                .dmcmReference(claim.getDmcmReference())
                .sbReference(claim.getSbReference())
                .sapDocNo(claim.getSapDocNo())
                .clearingDocNo(claim.getClearingDocNo())
                .orNumber(claim.getOrNumber())
                .exported(claim.isExported())
                .requiredDoc(claim.getRequiredDoc())
                .generalConfigVersion(claim.getGeneralConfigVersion())
                .build();

        // Verify conversion
        assertEquals(claim.getCode(), dto.getCode());
        assertEquals(claim.getStatus().name(), dto.getStatus().getCode());
        assertEquals(claim.getClaimableAmount(), dto.getClaimableAmount());
        assertEquals(claim.getDealer(), dto.getDealer());
        assertEquals(claim.getCreatedTime(), dto.getCreatedDate());
        assertEquals(claim.getUpdatedTime(), dto.getLastUpdatedDate());
        assertEquals(claim.getUpdatedBy(), dto.getLastUpdatedBy());
        assertEquals(claim.getReferenceNumber(), dto.getReferenceNumber());
        assertEquals(claim.getDmcmReference(), dto.getDmcmReference());
        assertEquals(claim.getSbReference(), dto.getSbReference());
        assertEquals(claim.getSapDocNo(), dto.getSapDocNo());
        assertEquals(claim.getClearingDocNo(), dto.getClearingDocNo());
        assertEquals(claim.getOrNumber(), dto.getOrNumber());
        assertEquals(claim.isExported(), dto.getExported());
        assertEquals(claim.getRequiredDoc(), dto.getRequiredDoc());
        assertEquals(claim.getGeneralConfigVersion(), dto.getGeneralConfigVersion());
    }
}
